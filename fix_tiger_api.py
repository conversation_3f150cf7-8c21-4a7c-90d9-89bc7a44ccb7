"""
Tiger Open API 修复脚本
解决 TigerOpenClient logger 属性缺失问题
"""

import os
import sys
import logging
from pathlib import Path


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def find_tiger_installation():
    """查找Tiger Open API安装路径"""
    try:
        import tigeropen
        tiger_path = Path(tigeropen.__file__).parent
        return tiger_path
    except ImportError:
        return None


def backup_original_file(file_path):
    """备份原始文件"""
    backup_path = f"{file_path}.backup"
    if not os.path.exists(backup_path):
        import shutil
        shutil.copy2(file_path, backup_path)
        logging.info(f"已备份原始文件到: {backup_path}")


def fix_tiger_client():
    """修复TigerOpenClient的logger问题"""
    logger = logging.getLogger(__name__)
    
    # 查找Tiger安装路径
    tiger_path = find_tiger_installation()
    if not tiger_path:
        logger.error("未找到Tiger Open API安装")
        return False
    
    client_file = tiger_path / "tiger_open_client.py"
    if not client_file.exists():
        logger.error(f"未找到tiger_open_client.py文件: {client_file}")
        return False
    
    logger.info(f"找到Tiger客户端文件: {client_file}")
    
    # 备份原始文件
    backup_original_file(str(client_file))
    
    # 读取原始文件
    with open(client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经修复
    if 'self._TigerOpenClient__logger = logging.getLogger(__name__)' in content:
        logger.info("文件已经修复过了")
        return True
    
    # 查找需要修复的位置
    if 'def _initialize(self):' in content:
        # 在_initialize方法开始处添加logger初始化
        old_pattern = 'def _initialize(self):'
        new_pattern = '''def _initialize(self):
        import logging
        self._TigerOpenClient__logger = logging.getLogger(__name__)'''
        
        content = content.replace(old_pattern, new_pattern)
        
        # 写入修复后的文件
        with open(client_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("Tiger客户端文件修复完成")
        return True
    else:
        logger.error("未找到_initialize方法，无法修复")
        return False


def test_tiger_fix():
    """测试修复是否成功"""
    logger = logging.getLogger(__name__)
    
    try:
        # 重新导入模块
        if 'tigeropen.tiger_open_client' in sys.modules:
            del sys.modules['tigeropen.tiger_open_client']
        if 'tigeropen' in sys.modules:
            del sys.modules['tigeropen']
        
        from tigeropen.tiger_open_config import TigerOpenClientConfig
        from tigeropen.tiger_open_client import TigerOpenClient
        
        # 尝试创建一个测试配置
        # 这里使用一个无效的配置来测试初始化过程
        try:
            # 创建一个临时的测试配置
            test_config = TigerOpenClientConfig()
            # 设置一些基本属性来避免其他错误
            test_config.tiger_id = "test"
            test_config.account = "test"
            test_config.private_key = "test"
            
            # 尝试创建客户端（这可能会因为无效配置而失败，但不应该是logger错误）
            client = TigerOpenClient(test_config)
            logger.info("✓ Tiger客户端创建成功，修复有效")
            return True
            
        except AttributeError as e:
            if '_TigerOpenClient__logger' in str(e):
                logger.error("✗ 修复失败，仍然存在logger问题")
                return False
            else:
                logger.info("✓ Logger问题已修复（其他配置错误是正常的）")
                return True
        except Exception as e:
            if '_TigerOpenClient__logger' not in str(e):
                logger.info("✓ Logger问题已修复（其他错误是正常的）")
                return True
            else:
                logger.error(f"✗ 修复失败: {e}")
                return False
                
    except Exception as e:
        logger.error(f"测试修复时出错: {e}")
        return False


def restore_backup():
    """恢复备份文件"""
    logger = logging.getLogger(__name__)
    
    tiger_path = find_tiger_installation()
    if not tiger_path:
        logger.error("未找到Tiger Open API安装")
        return False
    
    client_file = tiger_path / "tiger_open_client.py"
    backup_file = f"{client_file}.backup"
    
    if os.path.exists(backup_file):
        import shutil
        shutil.copy2(backup_file, str(client_file))
        logger.info("已恢复原始文件")
        return True
    else:
        logger.warning("未找到备份文件")
        return False


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("Tiger Open API 修复工具")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--restore':
        print("恢复原始文件...")
        if restore_backup():
            print("✓ 恢复成功")
        else:
            print("✗ 恢复失败")
        return
    
    try:
        # 检查Tiger API是否安装
        tiger_path = find_tiger_installation()
        if not tiger_path:
            print("✗ 未找到Tiger Open API安装")
            print("请先安装: pip install tigeropen")
            return
        
        print(f"找到Tiger Open API安装路径: {tiger_path}")
        
        # 尝试修复
        print("\n正在修复Tiger客户端...")
        if fix_tiger_client():
            print("✓ 修复完成")
            
            # 测试修复
            print("\n测试修复结果...")
            if test_tiger_fix():
                print("✓ 修复验证成功")
                print("\n现在可以正常使用Tiger Open API了！")
            else:
                print("✗ 修复验证失败")
        else:
            print("✗ 修复失败")
        
        print("\n注意：")
        print("- 原始文件已备份为 .backup")
        print("- 如需恢复原始文件，运行: python fix_tiger_api.py --restore")
        
    except Exception as e:
        logger.error(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
