"""
交易模块测试
测试交易记录器、交易分析器和实盘交易器功能
"""

import unittest
import os
import shutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import tempfile
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant_trading_system.trading.trade_recorder import TradeRecorder
from quant_trading_system.trading.trade_analyzer import TradeAnalyzer
from quant_trading_system.trading.live_trader import LiveTrader


class TestTradeRecorder(unittest.TestCase):
    """交易记录器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.trade_recorder = TradeRecorder()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_init_record_files(self):
        """测试记录文件初始化"""
        # 检查文件是否创建
        self.assertTrue(os.path.exists(self.trade_recorder.trades_file))
        self.assertTrue(os.path.exists(self.trade_recorder.orders_file))
        self.assertTrue(os.path.exists(self.trade_recorder.positions_file))
        
        # 检查文件结构
        trades_df = pd.read_csv(self.trade_recorder.trades_file)
        orders_df = pd.read_csv(self.trade_recorder.orders_file)
        positions_df = pd.read_csv(self.trade_recorder.positions_file)
        
        # 检查列名
        expected_trade_columns = ['trade_id', 'symbol', 'strategy', 'action', 'quantity']
        for col in expected_trade_columns:
            self.assertIn(col, trades_df.columns)
        
        expected_order_columns = ['order_id', 'symbol', 'strategy', 'action', 'quantity']
        for col in expected_order_columns:
            self.assertIn(col, orders_df.columns)
    
    def test_record_order(self):
        """测试订单记录"""
        order_data = {
            'symbol': 'AAPL',
            'strategy': 'TestStrategy',
            'action': 'BUY',
            'quantity': 100,
            'order_type': 'MKT',
            'price': 150.0,
            'notes': 'Test order'
        }
        
        order_id = self.trade_recorder.record_order(order_data)
        
        # 检查返回的订单ID
        self.assertIsInstance(order_id, str)
        self.assertTrue(order_id.startswith('ORD_'))
        
        # 检查记录是否保存
        orders_df = pd.read_csv(self.trade_recorder.orders_file)
        self.assertEqual(len(orders_df), 1)
        self.assertEqual(orders_df.iloc[0]['symbol'], 'AAPL')
        self.assertEqual(orders_df.iloc[0]['action'], 'BUY')
        self.assertEqual(orders_df.iloc[0]['quantity'], 100)
    
    def test_update_order(self):
        """测试订单更新"""
        # 先记录一个订单
        order_data = {
            'symbol': 'AAPL',
            'strategy': 'TestStrategy',
            'action': 'BUY',
            'quantity': 100,
            'order_type': 'MKT',
            'price': 150.0
        }
        
        order_id = self.trade_recorder.record_order(order_data)
        
        # 更新订单状态
        update_data = {
            'status': 'FILLED',
            'filled_quantity': 100,
            'avg_fill_price': 151.0
        }
        
        self.trade_recorder.update_order(order_id, update_data)
        
        # 检查更新结果
        orders_df = pd.read_csv(self.trade_recorder.orders_file)
        updated_order = orders_df[orders_df['order_id'] == order_id].iloc[0]
        
        self.assertEqual(updated_order['status'], 'FILLED')
        self.assertEqual(updated_order['filled_quantity'], 100)
        self.assertEqual(updated_order['avg_fill_price'], 151.0)
    
    def test_record_trade(self):
        """测试交易记录"""
        trade_data = {
            'symbol': 'AAPL',
            'strategy': 'TestStrategy',
            'action': 'BUY',
            'quantity': 100,
            'entry_price': 150.0,
            'exit_price': 155.0,
            'entry_time': datetime.now() - timedelta(hours=2),
            'exit_time': datetime.now(),
            'commission': 2.0,
            'trade_type': 'LONG'
        }
        
        trade_id = self.trade_recorder.record_trade(trade_data)
        
        # 检查返回的交易ID
        self.assertIsInstance(trade_id, str)
        self.assertTrue(trade_id.startswith('TRD_'))
        
        # 检查记录是否保存
        trades_df = pd.read_csv(self.trade_recorder.trades_file)
        self.assertEqual(len(trades_df), 1)
        
        trade_record = trades_df.iloc[0]
        self.assertEqual(trade_record['symbol'], 'AAPL')
        self.assertEqual(trade_record['quantity'], 100)
        self.assertEqual(trade_record['entry_price'], 150.0)
        self.assertEqual(trade_record['exit_price'], 155.0)
        
        # 检查计算的盈亏
        expected_pnl = (155.0 - 150.0) * 100  # 500
        self.assertEqual(trade_record['pnl'], expected_pnl)
        self.assertEqual(trade_record['net_pnl'], expected_pnl - 2.0)  # 减去手续费
    
    def test_record_position(self):
        """测试持仓记录"""
        position_data = {
            'symbol': 'AAPL',
            'strategy': 'TestStrategy',
            'quantity': 100,
            'avg_price': 150.0,
            'market_value': 15000.0,
            'unrealized_pnl': 500.0,
            'realized_pnl': 0.0,
            'total_pnl': 500.0
        }
        
        self.trade_recorder.record_position(position_data)
        
        # 检查记录是否保存
        positions_df = pd.read_csv(self.trade_recorder.positions_file)
        self.assertEqual(len(positions_df), 1)
        
        position_record = positions_df.iloc[0]
        self.assertEqual(position_record['symbol'], 'AAPL')
        self.assertEqual(position_record['quantity'], 100)
        self.assertEqual(position_record['avg_price'], 150.0)
    
    def test_get_trades(self):
        """测试交易查询"""
        # 记录几个测试交易
        trade_data_1 = {
            'symbol': 'AAPL',
            'strategy': 'Strategy1',
            'action': 'BUY',
            'quantity': 100,
            'entry_price': 150.0,
            'exit_price': 155.0,
            'entry_time': datetime.now() - timedelta(days=2),
            'exit_time': datetime.now() - timedelta(days=1),
            'commission': 2.0
        }
        
        trade_data_2 = {
            'symbol': 'MSFT',
            'strategy': 'Strategy2',
            'action': 'BUY',
            'quantity': 50,
            'entry_price': 200.0,
            'exit_price': 195.0,
            'entry_time': datetime.now() - timedelta(days=1),
            'exit_time': datetime.now(),
            'commission': 2.0
        }
        
        self.trade_recorder.record_trade(trade_data_1)
        self.trade_recorder.record_trade(trade_data_2)
        
        # 测试无过滤条件查询
        all_trades = self.trade_recorder.get_trades()
        self.assertEqual(len(all_trades), 2)
        
        # 测试按股票过滤
        aapl_trades = self.trade_recorder.get_trades(symbol='AAPL')
        self.assertEqual(len(aapl_trades), 1)
        self.assertEqual(aapl_trades.iloc[0]['symbol'], 'AAPL')
        
        # 测试按策略过滤
        strategy1_trades = self.trade_recorder.get_trades(strategy='Strategy1')
        self.assertEqual(len(strategy1_trades), 1)
        self.assertEqual(strategy1_trades.iloc[0]['strategy'], 'Strategy1')
    
    def test_get_orders(self):
        """测试订单查询"""
        # 记录测试订单
        order_data = {
            'symbol': 'AAPL',
            'strategy': 'TestStrategy',
            'action': 'BUY',
            'quantity': 100,
            'order_type': 'MKT',
            'price': 150.0
        }
        
        order_id = self.trade_recorder.record_order(order_data)
        
        # 查询订单
        orders = self.trade_recorder.get_orders()
        self.assertEqual(len(orders), 1)
        
        # 按股票查询
        aapl_orders = self.trade_recorder.get_orders(symbol='AAPL')
        self.assertEqual(len(aapl_orders), 1)
        
        # 按状态查询
        submitted_orders = self.trade_recorder.get_orders(status='SUBMITTED')
        self.assertEqual(len(submitted_orders), 1)


class TestTradeAnalyzer(unittest.TestCase):
    """交易分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.trade_recorder = TradeRecorder()
        self.trade_analyzer = TradeAnalyzer(self.trade_recorder)
        
        # 创建测试交易数据
        self._create_test_trades()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_trades(self):
        """创建测试交易数据"""
        # 创建一些盈利和亏损的交易
        trades = [
            {
                'symbol': 'AAPL', 'strategy': 'TestStrategy', 'action': 'BUY',
                'quantity': 100, 'entry_price': 150.0, 'exit_price': 155.0,
                'entry_time': datetime.now() - timedelta(days=10),
                'exit_time': datetime.now() - timedelta(days=9),
                'commission': 2.0, 'trade_type': 'LONG'
            },
            {
                'symbol': 'AAPL', 'strategy': 'TestStrategy', 'action': 'BUY',
                'quantity': 100, 'entry_price': 160.0, 'exit_price': 155.0,
                'entry_time': datetime.now() - timedelta(days=8),
                'exit_time': datetime.now() - timedelta(days=7),
                'commission': 2.0, 'trade_type': 'LONG'
            },
            {
                'symbol': 'MSFT', 'strategy': 'TestStrategy', 'action': 'BUY',
                'quantity': 50, 'entry_price': 200.0, 'exit_price': 210.0,
                'entry_time': datetime.now() - timedelta(days=6),
                'exit_time': datetime.now() - timedelta(days=5),
                'commission': 2.0, 'trade_type': 'LONG'
            }
        ]
        
        for trade_data in trades:
            self.trade_recorder.record_trade(trade_data)
    
    def test_calculate_performance_metrics(self):
        """测试交易指标计算"""
        trades_df = self.trade_recorder.get_trades()
        metrics = self.trade_analyzer.calculate_performance_metrics(trades_df)
        
        # 检查基本指标
        self.assertIn('total_trades', metrics)
        self.assertIn('winning_trades', metrics)
        self.assertIn('losing_trades', metrics)
        self.assertIn('win_rate', metrics)
        self.assertIn('total_pnl', metrics)
        self.assertIn('total_return', metrics)
        self.assertIn('sharpe_ratio', metrics)
        self.assertIn('max_drawdown', metrics)
        
        # 检查计算结果
        self.assertEqual(metrics['total_trades'], 3)
        self.assertEqual(metrics['winning_trades'], 2)  # AAPL盈利, MSFT盈利
        self.assertEqual(metrics['losing_trades'], 1)   # AAPL亏损
        
        # 检查胜率
        expected_win_rate = (2 / 3) * 100
        self.assertAlmostEqual(metrics['win_rate'], expected_win_rate, places=1)
    
    def test_analyze_strategy_performance(self):
        """测试策略表现分析"""
        analysis = self.trade_analyzer.analyze_strategy_performance(strategy='TestStrategy')
        
        # 检查分析结果结构
        self.assertIn('basic_metrics', analysis)
        self.assertIn('symbol_analysis', analysis)
        self.assertIn('time_analysis', analysis)
        self.assertIn('trade_distribution', analysis)
        
        # 检查股票分析
        symbol_analysis = analysis['symbol_analysis']
        self.assertIn('AAPL', symbol_analysis)
        self.assertIn('MSFT', symbol_analysis)
        
        # 检查AAPL的统计
        aapl_stats = symbol_analysis['AAPL']
        self.assertEqual(aapl_stats['total_trades'], 2)
    
    def test_generate_performance_report(self):
        """测试报告生成"""
        report_file = self.trade_analyzer.generate_performance_report(
            strategy='TestStrategy',
            save_charts=False  # 跳过图表生成以加快测试
        )
        
        # 检查报告文件是否生成
        if report_file:  # 如果成功生成报告
            self.assertTrue(os.path.exists(report_file))
            self.assertTrue(report_file.endswith('.html'))
            
            # 检查JSON报告是否也生成
            json_file = report_file.replace('.html', '.json')
            self.assertTrue(os.path.exists(json_file))
            
            # 检查JSON内容
            with open(json_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
            
            self.assertIn('basic_metrics', report_data)
            self.assertIn('symbol_analysis', report_data)


class TestLiveTraderIntegration(unittest.TestCase):
    """实盘交易器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建模拟的风险管理器
        class MockRiskManager:
            def check_trade_allowed(self, symbol, quantity, positions):
                return True
        
        self.risk_manager = MockRiskManager()
        self.trade_recorder = TradeRecorder()
        
        # 创建LiveTrader实例（不使用真实的Tiger客户端）
        self.live_trader = LiveTrader(
            tiger_client=None,  # 不使用真实客户端
            risk_manager=self.risk_manager,
            strategy_callback=lambda x: [],
            trade_recorder=self.trade_recorder
        )
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_trade_recorder_integration(self):
        """测试交易记录器集成"""
        # 检查LiveTrader是否正确初始化了交易记录器
        self.assertIsInstance(self.live_trader.trade_recorder, TradeRecorder)
        
        # 检查活跃订单跟踪
        self.assertIsInstance(self.live_trader.active_orders, dict)
        self.assertIsInstance(self.live_trader.open_positions, dict)
    
    def test_record_trade_completion(self):
        """测试交易完成记录"""
        # 模拟订单信息
        order_info = {
            'symbol': 'AAPL',
            'action': 'BUY',
            'quantity': 100
        }
        
        fill_data = {
            'filled_quantity': 100,
            'avg_fill_price': 150.0
        }
        
        # 测试交易完成记录
        try:
            self.live_trader._record_trade_completion('test_order_id', order_info, fill_data)
            
            # 检查持仓是否更新
            self.assertIn('AAPL', self.live_trader.open_positions)
            position = self.live_trader.open_positions['AAPL']
            self.assertEqual(position['quantity'], 100)
            self.assertEqual(position['avg_price'], 150.0)
            
        except Exception as e:
            # 由于没有真实的策略回调，可能会有一些错误，但不应该影响核心功能
            self.assertIsInstance(e, Exception)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
