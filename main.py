import yaml
import os
from tigeropen.tiger_open_config import get_client_config
from tigeropen.tiger_open_client import TigerOpenClient
from quant_trading_system.data.data_manager import DataManager
from quant_trading_system.risk.risk_manager import RiskManager, RiskLimits
from quant_trading_system.trading.live_trader import LiveTrader
import logging

def load_config():
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def setup_logging():
    # 确保logs目录存在
    os.makedirs('logs', exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/trading.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def create_tiger_client(config):
    """创建Tiger客户端"""
    logger = logging.getLogger(__name__)
    tiger_config = config['tiger']

    # 如果没有启用配置文件模式，直接返回None
    if not tiger_config.get('use_config_file', False):
        logger.info("Tiger配置文件模式未启用")
        return None

    try:
        from tigeropen.tiger_open_config import TigerOpenClientConfig

        props_path = tiger_config['props_path']
        if not os.path.exists(props_path):
            logger.warning(f"Tiger props path not found: {props_path}")
            return None

        config_file = os.path.join(props_path, 'tiger_openapi_config.properties')
        if not os.path.exists(config_file):
            logger.warning(f"Tiger config file not found: {config_file}")
            return None

        # 检查配置文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'your_tiger_id' in content or 'your_private_key_content_here' in content:
                logger.warning("Tiger config file contains placeholder values")
                return None

        # 创建配置
        client_config = TigerOpenClientConfig(props_path=props_path)

        # 验证配置是否有效
        if not hasattr(client_config, 'tiger_id') or not client_config.tiger_id:
            logger.warning("Tiger配置无效：缺少tiger_id")
            return None

        if not hasattr(client_config, 'private_key') or not client_config.private_key:
            logger.warning("Tiger配置无效：缺少private_key")
            return None

        # 尝试创建Tiger客户端
        tiger_client = TigerOpenClient(client_config)

        logger.info("Tiger客户端创建成功")
        return tiger_client

    except Exception as e:
        logger.warning(f"Tiger客户端创建失败: {e}")
        return None

def strategy_callback(market_data):
    """策略回调函数示例"""
    # 这里实现具体的策略逻辑
    # 返回交易信号列表
    signals = []
    
    # 示例：简单的动量策略信号
    for symbol, data in market_data.items():
        # 这里添加策略逻辑
        pass
    
    return signals

def main():
    # 加载配置
    config = load_config()
    setup_logging()
    
    logger = logging.getLogger(__name__)
    logger.info("量化交易系统启动")
    
    try:
        # 创建Tiger客户端
        tiger_client = create_tiger_client(config)

        # 初始化组件
        data_manager = DataManager(tiger_client)
        risk_limits = RiskLimits(**config['risk'])
        risk_manager = RiskManager(risk_limits)

        # 只有在Tiger客户端可用时才创建实盘交易器
        live_trader = None
        if tiger_client:
            live_trader = LiveTrader(
                tiger_client=tiger_client,
                risk_manager=risk_manager,
                strategy_callback=strategy_callback
            )
            logger.info("实盘交易器创建成功")
        else:
            logger.info("Tiger客户端不可用，跳过实盘交易器创建")

        # 测试数据获取（使用yfinance）
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        logger.info("测试历史数据获取...")
        for symbol in test_symbols:
            try:
                data = data_manager.fetch_historical_data(symbol, period="1mo")
                logger.info(f"{symbol} 数据获取完成，共 {len(data)} 条记录")
            except Exception as e:
                logger.warning(f"{symbol} 数据获取失败: {e}")

        # 测试实时行情（仅在Tiger客户端可用时）
        if tiger_client:
            logger.info("测试实时行情获取...")
            try:
                quotes = data_manager.get_realtime_quote(test_symbols)
                logger.info(f"获取到 {len(quotes)} 个股票的实时行情")
            except Exception as e:
                logger.warning(f"实时行情获取失败: {e}")
        else:
            logger.info("Tiger客户端不可用，跳过实时行情测试")

        logger.info("系统初始化完成")

        # 显示系统状态
        print("\n" + "="*50)
        print("量化交易系统状态")
        print("="*50)
        print(f"Tiger API连接: {'✓ 可用' if tiger_client else '✗ 不可用'}")
        print(f"历史数据获取: ✓ 可用 (yfinance)")
        print(f"实盘交易器: {'✓ 可用' if live_trader else '✗ 不可用'}")
        print("="*50)

        if not tiger_client:
            print("\n提示：要启用Tiger API功能，请：")
            print("1. 在Tiger开发者网站下载配置文件")
            print("2. 将配置文件放入 config/tiger_props/ 目录")
            print("3. 确保配置文件包含有效的认证信息")

        # 这里可以启动实盘交易
        # if live_trader:
        #     live_trader.start_trading()

        # 保持程序运行
        # while True:
        #     time.sleep(60)
        
    except KeyboardInterrupt:
        logger.info("系统正常关闭")
    except Exception as e:
        logger.error(f"系统异常: {e}", exc_info=True)

if __name__ == "__main__":
    main()
