"""
数据管理模块测试
测试多周期数据下载、保存、查看功能
"""

import unittest
import os
import shutil
import pandas as pd
from datetime import datetime, timedelta
import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant_trading_system.data.data_manager import DataManager


class TestDataManager(unittest.TestCase):
    """数据管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建数据管理器实例
        self.data_manager = DataManager(use_csv=True)
        
        # 测试股票代码
        self.test_symbols = ['AAPL', 'MSFT']
        self.test_timeframes = ['1d', '1h']
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_init_directories(self):
        """测试目录初始化"""
        # 检查数据目录是否创建
        self.assertTrue(os.path.exists('data'))
        self.assertTrue(os.path.exists('data/csv'))
        
        # 检查时间周期目录是否创建
        for timeframe in DataManager.TIMEFRAMES.keys():
            self.assertTrue(os.path.exists(f'data/csv/{timeframe}'))
    
    def test_get_csv_filepath(self):
        """测试CSV文件路径生成"""
        filepath = self.data_manager.get_csv_filepath('AAPL', '1d')
        expected_path = 'data/csv/1d/AAPL.csv'
        self.assertEqual(filepath, expected_path)
        
        # 测试无效时间周期
        with self.assertRaises(ValueError):
            self.data_manager.get_csv_filepath('AAPL', 'invalid')
    
    def test_preprocess_data(self):
        """测试数据预处理"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=5),
            'Open': [100, 101, 102, 103, 104],
            'High': [105, 106, 107, 108, 109],
            'Low': [95, 96, 97, 98, 99],
            'Close': [102, 103, 104, 105, 106],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        test_data.set_index('Date', inplace=True)
        
        # 预处理数据
        processed_data = self.data_manager._preprocess_data(test_data, 'AAPL')
        
        # 检查预处理结果
        self.assertIn('Symbol', processed_data.columns)
        self.assertEqual(processed_data['Symbol'].iloc[0], 'AAPL')
        self.assertIn('Date', processed_data.columns)
        self.assertEqual(len(processed_data), 5)
    
    def test_save_and_load_csv(self):
        """测试CSV保存和加载"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=3),
            'Open': [100, 101, 102],
            'High': [105, 106, 107],
            'Low': [95, 96, 97],
            'Close': [102, 103, 104],
            'Volume': [1000, 1100, 1200],
            'Symbol': ['AAPL', 'AAPL', 'AAPL']
        })
        
        # 保存到CSV
        self.data_manager._save_to_csv(test_data, 'AAPL', '1d')
        
        # 检查文件是否创建
        filepath = self.data_manager.get_csv_filepath('AAPL', '1d')
        self.assertTrue(os.path.exists(filepath))
        
        # 加载数据
        loaded_data = self.data_manager.load_stock_data('AAPL', '1d')
        
        # 检查加载的数据
        self.assertEqual(len(loaded_data), 3)
        self.assertIn('Symbol', loaded_data.columns)
    
    def test_get_us_stock_list(self):
        """测试获取美股股票列表"""
        # 这个测试可能需要网络连接，所以我们只检查返回类型
        stock_list = self.data_manager.get_us_stock_list()
        self.assertIsInstance(stock_list, list)
        self.assertGreater(len(stock_list), 0)
        
        # 检查是否包含常见股票
        common_stocks = ['AAPL', 'MSFT', 'GOOGL']
        for stock in common_stocks:
            if stock in stock_list:
                self.assertIn(stock, stock_list)
                break
    
    def test_list_available_symbols(self):
        """测试列出可用股票代码"""
        # 创建测试CSV文件
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=2),
            'Open': [100, 101],
            'High': [105, 106],
            'Low': [95, 96],
            'Close': [102, 103],
            'Volume': [1000, 1100],
            'Symbol': ['AAPL', 'AAPL']
        })
        
        self.data_manager._save_to_csv(test_data, 'AAPL', '1d')
        self.data_manager._save_to_csv(test_data, 'MSFT', '1d')
        
        # 获取可用股票代码
        symbols = self.data_manager.list_available_symbols('1d')
        
        # 检查结果
        self.assertIn('AAPL', symbols)
        self.assertIn('MSFT', symbols)
        self.assertEqual(len(symbols), 2)
    
    def test_get_data_summary(self):
        """测试数据概览"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=2),
            'Open': [100, 101],
            'High': [105, 106],
            'Low': [95, 96],
            'Close': [102, 103],
            'Volume': [1000, 1100],
            'Symbol': ['AAPL', 'AAPL']
        })
        
        # 保存测试数据
        self.data_manager._save_to_csv(test_data, 'AAPL', '1d')
        self.data_manager._save_to_csv(test_data, 'MSFT', '1h')
        
        # 获取数据概览
        summary = self.data_manager.get_data_summary()
        
        # 检查概览结果
        self.assertIn('timeframes', summary)
        self.assertIn('total_symbols', summary)
        self.assertIn('total_files', summary)
        self.assertGreater(summary['total_files'], 0)
    
    def test_export_import_data(self):
        """测试数据导出和导入"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=2),
            'Open': [100, 101],
            'High': [105, 106],
            'Low': [95, 96],
            'Close': [102, 103],
            'Volume': [1000, 1100],
            'Symbol': ['AAPL', 'AAPL']
        })
        
        # 保存测试数据
        self.data_manager._save_to_csv(test_data, 'AAPL', '1d')
        
        # 导出数据
        export_path = 'test_export'
        success = self.data_manager.export_all_data(export_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(export_path))
        
        # 删除原数据
        if os.path.exists('data/csv'):
            shutil.rmtree('data/csv')
        
        # 导入数据
        success = self.data_manager.import_data(export_path)
        self.assertTrue(success)
        
        # 检查导入的数据
        loaded_data = self.data_manager.load_stock_data('AAPL', '1d')
        self.assertEqual(len(loaded_data), 2)


class TestDataManagerIntegration(unittest.TestCase):
    """数据管理器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.data_manager = DataManager(use_csv=True)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_download_and_load_workflow(self):
        """测试完整的下载和加载工作流"""
        # 注意：这个测试需要网络连接，在CI环境中可能需要跳过
        try:
            # 下载数据（使用较短的周期以加快测试）
            data = self.data_manager.download_stock_data('AAPL', '1d', period='5d')
            
            if not data.empty:
                # 检查下载的数据
                self.assertGreater(len(data), 0)
                self.assertIn('Symbol', data.columns)
                
                # 重新加载数据
                loaded_data = self.data_manager.load_stock_data('AAPL', '1d')
                self.assertGreater(len(loaded_data), 0)
                
                # 检查数据一致性
                self.assertEqual(len(data), len(loaded_data))
            else:
                self.skipTest("无法下载测试数据，可能是网络问题")
                
        except Exception as e:
            self.skipTest(f"网络相关测试跳过: {e}")


if __name__ == '__main__':
    # 创建测试目录
    os.makedirs('tests', exist_ok=True)
    
    # 运行测试
    unittest.main(verbosity=2)
