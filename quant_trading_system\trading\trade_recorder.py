"""
交易记录器
保存和管理所有交易记录细节
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path


class TradeRecorder:
    """
    交易记录器
    
    功能特性:
    1. 记录所有交易细节
    2. 支持CSV和JSON格式存储
    3. 提供交易记录查询功能
    4. 计算交易统计指标
    """
    
    def __init__(self, records_dir: str = "trading_records"):
        """
        初始化交易记录器
        
        Args:
            records_dir: 记录保存目录
        """
        self.records_dir = records_dir
        self.logger = logging.getLogger(__name__)
        
        # 创建记录目录
        os.makedirs(self.records_dir, exist_ok=True)
        os.makedirs(f"{self.records_dir}/trades", exist_ok=True)
        os.makedirs(f"{self.records_dir}/orders", exist_ok=True)
        os.makedirs(f"{self.records_dir}/positions", exist_ok=True)
        
        # 交易记录文件路径
        self.trades_file = f"{self.records_dir}/trades/trades.csv"
        self.orders_file = f"{self.records_dir}/orders/orders.csv"
        self.positions_file = f"{self.records_dir}/positions/positions.csv"
        
        # 初始化记录文件
        self._init_record_files()
        
        self.logger.info("交易记录器初始化完成")
    
    def _init_record_files(self):
        """初始化记录文件"""
        try:
            # 交易记录表结构
            if not os.path.exists(self.trades_file):
                trades_columns = [
                    'trade_id', 'symbol', 'strategy', 'action', 'quantity', 
                    'entry_price', 'exit_price', 'entry_time', 'exit_time',
                    'pnl', 'pnl_percent', 'commission', 'net_pnl',
                    'holding_period', 'trade_type', 'notes'
                ]
                pd.DataFrame(columns=trades_columns).to_csv(self.trades_file, index=False)
            
            # 订单记录表结构
            if not os.path.exists(self.orders_file):
                orders_columns = [
                    'order_id', 'symbol', 'strategy', 'action', 'quantity',
                    'order_type', 'price', 'status', 'create_time', 'fill_time',
                    'filled_quantity', 'avg_fill_price', 'commission', 'notes'
                ]
                pd.DataFrame(columns=orders_columns).to_csv(self.orders_file, index=False)
            
            # 持仓记录表结构
            if not os.path.exists(self.positions_file):
                positions_columns = [
                    'timestamp', 'symbol', 'strategy', 'quantity', 'avg_price',
                    'market_value', 'unrealized_pnl', 'realized_pnl', 'total_pnl'
                ]
                pd.DataFrame(columns=positions_columns).to_csv(self.positions_file, index=False)
            
            self.logger.info("记录文件初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化记录文件失败: {e}")
    
    def record_order(self, order_data: Dict[str, Any]) -> str:
        """
        记录订单
        
        Args:
            order_data: 订单数据字典
            
        Returns:
            订单ID
        """
        try:
            # 生成订单ID
            order_id = f"ORD_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # 准备订单记录
            order_record = {
                'order_id': order_id,
                'symbol': order_data.get('symbol', ''),
                'strategy': order_data.get('strategy', ''),
                'action': order_data.get('action', ''),
                'quantity': order_data.get('quantity', 0),
                'order_type': order_data.get('order_type', 'MKT'),
                'price': order_data.get('price', 0),
                'status': order_data.get('status', 'SUBMITTED'),
                'create_time': datetime.now().isoformat(),
                'fill_time': '',
                'filled_quantity': 0,
                'avg_fill_price': 0,
                'commission': 0,
                'notes': order_data.get('notes', '')
            }
            
            # 保存到CSV
            self._append_to_csv(self.orders_file, order_record)
            
            self.logger.info(f"订单记录已保存: {order_id}")
            return order_id
            
        except Exception as e:
            self.logger.error(f"记录订单失败: {e}")
            return ""
    
    def update_order(self, order_id: str, update_data: Dict[str, Any]):
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            update_data: 更新数据
        """
        try:
            # 读取订单记录
            orders_df = pd.read_csv(self.orders_file)
            
            # 查找并更新订单
            mask = orders_df['order_id'] == order_id
            if mask.any():
                for key, value in update_data.items():
                    if key in orders_df.columns:
                        orders_df.loc[mask, key] = value
                
                # 如果订单完成，记录完成时间
                if update_data.get('status') == 'FILLED':
                    orders_df.loc[mask, 'fill_time'] = datetime.now().isoformat()
                
                # 保存更新后的记录
                orders_df.to_csv(self.orders_file, index=False)
                
                self.logger.info(f"订单更新完成: {order_id}")
            else:
                self.logger.warning(f"未找到订单: {order_id}")
                
        except Exception as e:
            self.logger.error(f"更新订单失败: {e}")
    
    def record_trade(self, trade_data: Dict[str, Any]) -> str:
        """
        记录交易
        
        Args:
            trade_data: 交易数据字典
            
        Returns:
            交易ID
        """
        try:
            # 生成交易ID
            trade_id = f"TRD_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # 计算盈亏
            entry_price = trade_data.get('entry_price', 0)
            exit_price = trade_data.get('exit_price', 0)
            quantity = trade_data.get('quantity', 0)
            commission = trade_data.get('commission', 0)
            
            if trade_data.get('action') == 'BUY':
                pnl = (exit_price - entry_price) * quantity
            else:  # SELL
                pnl = (entry_price - exit_price) * quantity
            
            pnl_percent = (pnl / (entry_price * quantity)) * 100 if entry_price > 0 else 0
            net_pnl = pnl - commission
            
            # 计算持仓时间
            entry_time = pd.to_datetime(trade_data.get('entry_time', datetime.now()))
            exit_time = pd.to_datetime(trade_data.get('exit_time', datetime.now()))
            holding_period = (exit_time - entry_time).total_seconds() / 3600  # 小时
            
            # 准备交易记录
            trade_record = {
                'trade_id': trade_id,
                'symbol': trade_data.get('symbol', ''),
                'strategy': trade_data.get('strategy', ''),
                'action': trade_data.get('action', ''),
                'quantity': quantity,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'entry_time': entry_time.isoformat(),
                'exit_time': exit_time.isoformat(),
                'pnl': pnl,
                'pnl_percent': pnl_percent,
                'commission': commission,
                'net_pnl': net_pnl,
                'holding_period': holding_period,
                'trade_type': trade_data.get('trade_type', 'LONG'),
                'notes': trade_data.get('notes', '')
            }
            
            # 保存到CSV
            self._append_to_csv(self.trades_file, trade_record)
            
            self.logger.info(f"交易记录已保存: {trade_id}")
            return trade_id
            
        except Exception as e:
            self.logger.error(f"记录交易失败: {e}")
            return ""
    
    def record_position(self, position_data: Dict[str, Any]):
        """
        记录持仓快照
        
        Args:
            position_data: 持仓数据字典
        """
        try:
            # 准备持仓记录
            position_record = {
                'timestamp': datetime.now().isoformat(),
                'symbol': position_data.get('symbol', ''),
                'strategy': position_data.get('strategy', ''),
                'quantity': position_data.get('quantity', 0),
                'avg_price': position_data.get('avg_price', 0),
                'market_value': position_data.get('market_value', 0),
                'unrealized_pnl': position_data.get('unrealized_pnl', 0),
                'realized_pnl': position_data.get('realized_pnl', 0),
                'total_pnl': position_data.get('total_pnl', 0)
            }
            
            # 保存到CSV
            self._append_to_csv(self.positions_file, position_record)
            
            self.logger.debug(f"持仓记录已保存: {position_data.get('symbol', '')}")
            
        except Exception as e:
            self.logger.error(f"记录持仓失败: {e}")
    
    def _append_to_csv(self, file_path: str, record: Dict[str, Any]):
        """
        追加记录到CSV文件
        
        Args:
            file_path: 文件路径
            record: 记录字典
        """
        try:
            # 读取现有数据
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
            else:
                df = pd.DataFrame()
            
            # 追加新记录
            new_record_df = pd.DataFrame([record])
            df = pd.concat([df, new_record_df], ignore_index=True)
            
            # 保存到文件
            df.to_csv(file_path, index=False)
            
        except Exception as e:
            self.logger.error(f"追加记录到CSV失败: {e}")
    
    def get_trades(self, symbol: str = None, strategy: str = None, 
                   start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        查询交易记录
        
        Args:
            symbol: 股票代码
            strategy: 策略名称
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易记录DataFrame
        """
        try:
            if not os.path.exists(self.trades_file):
                return pd.DataFrame()
            
            df = pd.read_csv(self.trades_file)
            
            # 应用过滤条件
            if symbol:
                df = df[df['symbol'] == symbol]
            
            if strategy:
                df = df[df['strategy'] == strategy]
            
            if start_date:
                df['entry_time'] = pd.to_datetime(df['entry_time'])
                df = df[df['entry_time'] >= pd.to_datetime(start_date)]
            
            if end_date:
                df['entry_time'] = pd.to_datetime(df['entry_time'])
                df = df[df['entry_time'] <= pd.to_datetime(end_date)]
            
            return df
            
        except Exception as e:
            self.logger.error(f"查询交易记录失败: {e}")
            return pd.DataFrame()
    
    def get_orders(self, symbol: str = None, status: str = None) -> pd.DataFrame:
        """
        查询订单记录
        
        Args:
            symbol: 股票代码
            status: 订单状态
            
        Returns:
            订单记录DataFrame
        """
        try:
            if not os.path.exists(self.orders_file):
                return pd.DataFrame()
            
            df = pd.read_csv(self.orders_file)
            
            # 应用过滤条件
            if symbol:
                df = df[df['symbol'] == symbol]
            
            if status:
                df = df[df['status'] == status]
            
            return df
            
        except Exception as e:
            self.logger.error(f"查询订单记录失败: {e}")
            return pd.DataFrame()
    
    def get_positions(self, symbol: str = None, latest_only: bool = True) -> pd.DataFrame:
        """
        查询持仓记录
        
        Args:
            symbol: 股票代码
            latest_only: 是否只返回最新记录
            
        Returns:
            持仓记录DataFrame
        """
        try:
            if not os.path.exists(self.positions_file):
                return pd.DataFrame()
            
            df = pd.read_csv(self.positions_file)
            
            # 应用过滤条件
            if symbol:
                df = df[df['symbol'] == symbol]
            
            if latest_only and not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp').groupby(['symbol', 'strategy']).tail(1)
            
            return df
            
        except Exception as e:
            self.logger.error(f"查询持仓记录失败: {e}")
            return pd.DataFrame()
