"""
图表生成器
生成各种交易和分析图表
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import os
from pathlib import Path

# 可选依赖
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    HAS_PLOTLY = True
except ImportError:
    HAS_PLOTLY = False


class ChartGenerator:
    """
    图表生成器
    
    功能特性:
    1. 价格和技术指标图表
    2. 交易信号可视化
    3. 投资组合表现图表
    4. 风险分析图表
    5. 支持静态和交互式图表
    """
    
    def __init__(self, output_dir: str = "charts"):
        """
        初始化图表生成器
        
        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置图表样式
        self._setup_styles()
        
        self.logger.info("图表生成器初始化完成")
    
    def _setup_styles(self):
        """设置图表样式"""
        try:
            # 设置matplotlib样式
            plt.style.use('default')
            plt.rcParams['figure.figsize'] = (12, 8)
            plt.rcParams['font.size'] = 10
            plt.rcParams['axes.grid'] = True
            plt.rcParams['grid.alpha'] = 0.3
            
            # 设置seaborn样式（如果可用）
            if HAS_SEABORN:
                sns.set_palette("husl")
                
        except Exception as e:
            self.logger.warning(f"设置图表样式失败: {e}")
    
    def plot_price_chart(self, data: pd.DataFrame, symbol: str, 
                        indicators: Dict[str, pd.Series] = None,
                        signals: List[Dict] = None,
                        save_path: str = None) -> str:
        """
        绘制价格图表
        
        Args:
            data: 价格数据DataFrame (包含Date, Open, High, Low, Close, Volume列)
            symbol: 股票代码
            indicators: 技术指标字典 {'MA5': Series, 'MA20': Series, ...}
            signals: 交易信号列表 [{'date': date, 'action': 'BUY', 'price': price}, ...]
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        try:
            # 创建子图
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), 
                                          gridspec_kw={'height_ratios': [3, 1]})
            
            # 确保日期列是datetime类型
            if 'Date' in data.columns:
                data['Date'] = pd.to_datetime(data['Date'])
                dates = data['Date']
            else:
                dates = data.index
            
            # 绘制价格线
            ax1.plot(dates, data['Close'], label=f'{symbol} Close', linewidth=2, color='blue')
            
            # 绘制技术指标
            if indicators:
                colors = ['red', 'green', 'orange', 'purple', 'brown']
                for i, (name, series) in enumerate(indicators.items()):
                    color = colors[i % len(colors)]
                    ax1.plot(dates, series, label=name, linewidth=1.5, color=color, alpha=0.8)
            
            # 绘制交易信号
            if signals:
                buy_signals = [s for s in signals if s.get('action') == 'BUY']
                sell_signals = [s for s in signals if s.get('action') == 'SELL']
                
                if buy_signals:
                    buy_dates = [pd.to_datetime(s['date']) for s in buy_signals]
                    buy_prices = [s['price'] for s in buy_signals]
                    ax1.scatter(buy_dates, buy_prices, color='green', marker='^', 
                              s=100, label='Buy Signal', zorder=5)
                
                if sell_signals:
                    sell_dates = [pd.to_datetime(s['date']) for s in sell_signals]
                    sell_prices = [s['price'] for s in sell_signals]
                    ax1.scatter(sell_dates, sell_prices, color='red', marker='v', 
                              s=100, label='Sell Signal', zorder=5)
            
            # 设置价格图表
            ax1.set_title(f'{symbol} Price Chart with Indicators', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Price ($)', fontsize=12)
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax1.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
            
            # 绘制成交量
            if 'Volume' in data.columns:
                ax2.bar(dates, data['Volume'], alpha=0.6, color='gray', width=1)
                ax2.set_ylabel('Volume', fontsize=12)
                ax2.set_xlabel('Date', fontsize=12)
                ax2.grid(True, alpha=0.3)
                
                # 格式化成交量显示
                ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
            
            plt.tight_layout()
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f"{self.output_dir}/{symbol}_price_chart_{timestamp}.png"
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"价格图表已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制价格图表失败: {e}")
            return ""
    
    def plot_portfolio_performance(self, portfolio_data: pd.DataFrame,
                                 benchmark_data: pd.DataFrame = None,
                                 save_path: str = None) -> str:
        """
        绘制投资组合表现图表
        
        Args:
            portfolio_data: 投资组合数据 (包含Date和Value列)
            benchmark_data: 基准数据 (包含Date和Value列)
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # 确保日期列是datetime类型
            portfolio_data['Date'] = pd.to_datetime(portfolio_data['Date'])
            dates = portfolio_data['Date']
            
            # 计算收益率
            portfolio_data['Returns'] = portfolio_data['Value'].pct_change()
            portfolio_data['Cumulative_Returns'] = (1 + portfolio_data['Returns']).cumprod() - 1
            
            # 1. 投资组合价值曲线
            ax1.plot(dates, portfolio_data['Value'], label='Portfolio Value', linewidth=2, color='blue')
            
            if benchmark_data is not None:
                benchmark_data['Date'] = pd.to_datetime(benchmark_data['Date'])
                ax1.plot(benchmark_data['Date'], benchmark_data['Value'], 
                        label='Benchmark', linewidth=2, color='red', alpha=0.7)
            
            ax1.set_title('Portfolio Value Over Time', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Portfolio Value ($)', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 累积收益率
            ax2.plot(dates, portfolio_data['Cumulative_Returns'] * 100, 
                    label='Cumulative Returns', linewidth=2, color='green')
            ax2.set_title('Cumulative Returns (%)', fontsize=14, fontweight='bold')
            ax2.set_ylabel('Returns (%)', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 回撤分析
            peak = portfolio_data['Value'].expanding().max()
            drawdown = (portfolio_data['Value'] - peak) / peak * 100
            ax3.fill_between(dates, drawdown, 0, alpha=0.3, color='red')
            ax3.plot(dates, drawdown, color='red', linewidth=1)
            ax3.set_title('Drawdown Analysis', fontsize=14, fontweight='bold')
            ax3.set_ylabel('Drawdown (%)', fontsize=12)
            ax3.grid(True, alpha=0.3)
            
            # 4. 收益率分布
            returns_clean = portfolio_data['Returns'].dropna()
            ax4.hist(returns_clean * 100, bins=50, alpha=0.7, color='blue', edgecolor='black')
            ax4.axvline(returns_clean.mean() * 100, color='red', linestyle='--', 
                       label=f'Mean: {returns_clean.mean()*100:.2f}%')
            ax4.set_title('Returns Distribution', fontsize=14, fontweight='bold')
            ax4.set_xlabel('Daily Returns (%)', fontsize=12)
            ax4.set_ylabel('Frequency', fontsize=12)
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 格式化日期轴
            for ax in [ax1, ax2, ax3]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                ax.xaxis.set_major_locator(mdates.MonthLocator())
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f"{self.output_dir}/portfolio_performance_{timestamp}.png"
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"投资组合表现图表已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制投资组合表现图表失败: {e}")
            return ""
    
    def plot_risk_metrics(self, risk_data: Dict[str, Any], save_path: str = None) -> str:
        """
        绘制风险指标图表
        
        Args:
            risk_data: 风险数据字典
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # 1. VaR分析
            if 'var_history' in risk_data:
                var_data = risk_data['var_history']
                dates = pd.to_datetime(var_data['dates'])
                ax1.plot(dates, var_data['var_5pct'], label='5% VaR', linewidth=2, color='orange')
                ax1.plot(dates, var_data['var_1pct'], label='1% VaR', linewidth=2, color='red')
                ax1.set_title('Value at Risk (VaR) Over Time', fontsize=14, fontweight='bold')
                ax1.set_ylabel('VaR', fontsize=12)
                ax1.legend()
                ax1.grid(True, alpha=0.3)
            
            # 2. 持仓集中度
            if 'position_concentration' in risk_data:
                positions = risk_data['position_concentration']
                symbols = list(positions.keys())
                weights = list(positions.values())
                
                ax2.pie(weights, labels=symbols, autopct='%1.1f%%', startangle=90)
                ax2.set_title('Position Concentration', fontsize=14, fontweight='bold')
            
            # 3. 行业敞口
            if 'sector_exposure' in risk_data:
                sectors = risk_data['sector_exposure']
                sector_names = list(sectors.keys())
                exposures = list(sectors.values())
                
                ax3.bar(sector_names, exposures, color='skyblue', alpha=0.7)
                ax3.set_title('Sector Exposure', fontsize=14, fontweight='bold')
                ax3.set_ylabel('Exposure (%)', fontsize=12)
                ax3.tick_params(axis='x', rotation=45)
                ax3.grid(True, alpha=0.3)
            
            # 4. 风险指标仪表盘
            if 'risk_metrics' in risk_data:
                metrics = risk_data['risk_metrics']
                metric_names = list(metrics.keys())
                metric_values = list(metrics.values())
                
                colors = ['green' if v < 0.05 else 'yellow' if v < 0.1 else 'red' for v in metric_values]
                bars = ax4.barh(metric_names, metric_values, color=colors, alpha=0.7)
                ax4.set_title('Risk Metrics Dashboard', fontsize=14, fontweight='bold')
                ax4.set_xlabel('Risk Level', fontsize=12)
                ax4.grid(True, alpha=0.3)
                
                # 添加数值标签
                for i, (bar, value) in enumerate(zip(bars, metric_values)):
                    ax4.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2, 
                            f'{value:.3f}', ha='left', va='center')
            
            plt.tight_layout()
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f"{self.output_dir}/risk_metrics_{timestamp}.png"
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"风险指标图表已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制风险指标图表失败: {e}")
            return ""
    
    def create_interactive_chart(self, data: pd.DataFrame, chart_type: str = 'candlestick',
                               save_path: str = None) -> str:
        """
        创建交互式图表 (需要plotly)
        
        Args:
            data: 数据DataFrame
            chart_type: 图表类型 ('candlestick', 'line', 'scatter')
            save_path: 保存路径
            
        Returns:
            HTML文件路径
        """
        if not HAS_PLOTLY:
            self.logger.warning("Plotly未安装，无法创建交互式图表")
            return ""
        
        try:
            if chart_type == 'candlestick':
                fig = go.Figure(data=go.Candlestick(
                    x=data['Date'],
                    open=data['Open'],
                    high=data['High'],
                    low=data['Low'],
                    close=data['Close']
                ))
                fig.update_layout(title='Interactive Candlestick Chart')
            
            elif chart_type == 'line':
                fig = go.Figure()
                fig.add_trace(go.Scatter(x=data['Date'], y=data['Close'], 
                                       mode='lines', name='Close Price'))
                fig.update_layout(title='Interactive Line Chart')
            
            # 保存为HTML
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f"{self.output_dir}/interactive_chart_{timestamp}.html"
            
            fig.write_html(save_path)
            
            self.logger.info(f"交互式图表已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"创建交互式图表失败: {e}")
            return ""
