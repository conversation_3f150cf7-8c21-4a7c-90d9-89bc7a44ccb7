"""
回测引擎
使用Backtrader进行策略回测
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
from datetime import datetime, timedelta
import logging
import os


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_cash=100000, commission=0.001):
        self.initial_cash = initial_cash
        self.commission = commission
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        os.makedirs('results', exist_ok=True)
        os.makedirs('results/plots', exist_ok=True)
    
    def get_data(self, symbol, start_date, end_date):
        """获取股票数据"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if data.empty:
                raise ValueError(f"No data found for {symbol}")
            
            self.logger.info(f"Downloaded {len(data)} days of data for {symbol}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to download data for {symbol}: {e}")
            raise
    
    def run_backtest(self, strategy_class, symbol='AAPL', 
                    start_date='2023-01-01', end_date='2024-01-01',
                    strategy_params=None, plot=True):
        """运行回测"""
        
        self.logger.info(f"Starting backtest for {symbol} from {start_date} to {end_date}")
        
        # 创建Cerebro引擎
        cerebro = bt.Cerebro()
        
        # 添加策略
        if strategy_params:
            cerebro.addstrategy(strategy_class, **strategy_params)
        else:
            cerebro.addstrategy(strategy_class)
        
        # 获取数据
        data = self.get_data(symbol, start_date, end_date)
        
        # 转换为Backtrader数据格式
        bt_data = bt.feeds.PandasData(
            dataname=data,
            datetime=None,
            open='Open',
            high='High',
            low='Low',
            close='Close',
            volume='Volume',
            openinterest=None
        )
        
        # 添加数据到引擎
        cerebro.adddata(bt_data)
        
        # 设置初始资金
        cerebro.broker.setcash(self.initial_cash)
        
        # 设置手续费
        cerebro.broker.setcommission(commission=self.commission)
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        # 记录初始资金
        initial_value = cerebro.broker.getvalue()
        self.logger.info(f'Initial Portfolio Value: ${initial_value:,.2f}')
        
        # 运行回测
        results = cerebro.run()
        
        # 记录最终资金
        final_value = cerebro.broker.getvalue()
        self.logger.info(f'Final Portfolio Value: ${final_value:,.2f}')
        
        # 获取分析结果
        strat = results[0]
        
        # 计算收益率
        total_return = (final_value - initial_value) / initial_value * 100
        
        # 获取分析器结果
        sharpe_ratio = strat.analyzers.sharpe.get_analysis().get('sharperatio', 0)
        drawdown = strat.analyzers.drawdown.get_analysis()
        returns_analysis = strat.analyzers.returns.get_analysis()
        trades_analysis = strat.analyzers.trades.get_analysis()
        
        # 准备结果
        results_dict = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'initial_value': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio if sharpe_ratio else 0,
            'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
            'total_trades': trades_analysis.get('total', {}).get('total', 0),
            'winning_trades': trades_analysis.get('won', {}).get('total', 0),
            'losing_trades': trades_analysis.get('lost', {}).get('total', 0),
            'win_rate': 0
        }
        
        # 计算胜率
        if results_dict['total_trades'] > 0:
            results_dict['win_rate'] = results_dict['winning_trades'] / results_dict['total_trades'] * 100
        
        # 打印结果
        self.print_results(results_dict)
        
        # 绘制图表
        if plot:
            try:
                self.plot_results(cerebro, symbol, start_date, end_date)
            except Exception as e:
                self.logger.warning(f"Failed to generate plot: {e}")
        
        return results_dict, cerebro
    
    def print_results(self, results):
        """打印回测结果"""
        print("\n" + "="*60)
        print("BACKTEST RESULTS")
        print("="*60)
        print(f"Symbol: {results['symbol']}")
        print(f"Period: {results['start_date']} to {results['end_date']}")
        print(f"Initial Value: ${results['initial_value']:,.2f}")
        print(f"Final Value: ${results['final_value']:,.2f}")
        print(f"Total Return: {results['total_return']:.2f}%")
        print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
        print(f"Total Trades: {results['total_trades']}")
        print(f"Winning Trades: {results['winning_trades']}")
        print(f"Losing Trades: {results['losing_trades']}")
        print(f"Win Rate: {results['win_rate']:.2f}%")
        print("="*60)
    
    def plot_results(self, cerebro, symbol, start_date, end_date):
        """绘制回测结果图表"""
        filename = f"results/plots/{symbol}_backtest_{start_date}_{end_date}.png"
        
        # 使用matplotlib绘制
        fig = cerebro.plot(style='candlestick', barup='green', bardown='red')[0][0]
        fig.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        
        self.logger.info(f"Plot saved to {filename}")
        
        return filename
