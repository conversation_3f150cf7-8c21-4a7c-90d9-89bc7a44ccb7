import backtrader as bt
from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseStrategy(bt.Strategy, ABC):
    """策略基类"""
    
    params = (
        ('risk_pct', 0.02),  # 单笔风险比例
        ('max_positions', 5),  # 最大持仓数
    )
    
    def __init__(self):
        self.order = None
        self.positions_count = 0
        
    @abstractmethod
    def define_indicators(self):
        """定义技术指标"""
        pass
    
    @abstractmethod
    def entry_signal(self) -> bool:
        """入场信号"""
        pass
    
    @abstractmethod
    def exit_signal(self) -> bool:
        """出场信号"""
        pass
    
    def next(self):
        if self.order:
            return
            
        if not self.position:
            if self.entry_signal() and self.positions_count < self.params.max_positions:
                size = self._calculate_position_size()
                self.order = self.buy(size=size)
                self.positions_count += 1
        else:
            if self.exit_signal():
                self.order = self.sell()
                self.positions_count -= 1
    
    def _calculate_position_size(self) -> int:
        """计算仓位大小"""
        risk_amount = self.broker.get_cash() * self.params.risk_pct
        price = self.data.close[0]
        return int(risk_amount / price)