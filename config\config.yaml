# 量化交易系统配置

# Tiger API配置 - 使用配置文件方式
tiger:
  props_path: "config/tiger_props/"  # Tiger配置文件路径
  use_config_file: true  # 使用配置文件方式
  # 备用配置（如果不使用配置文件方式）
  
# 数据库配置
database:
  path: "data/market_data.db"
  backup_interval: 3600  # 1小时备份一次

# 风险管理配置
risk:
  max_drawdown: 0.10
  max_position_size: 0.90
  max_daily_trades: 50
  max_total_exposure: 0.80

# 策略配置
strategy:
  default_risk_pct: 0.02
  max_positions: 5
  
# 监控配置
monitoring:
  alert_email: "<EMAIL>"
  log_level: "INFO"
  heartbeat_interval: 60