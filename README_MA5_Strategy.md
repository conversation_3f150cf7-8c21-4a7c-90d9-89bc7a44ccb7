# Apple股票5日均线策略测试系统

这个系统实现了一个基于5日移动平均线的Apple股票交易策略，包括回测和实盘交易功能。

## 策略说明

**5日均线策略**：
- **买入信号**：当股价上穿5日均线时买入
- **卖出信号**：当股价下穿5日均线时卖出
- **仓位管理**：使用95%的可用资金进行交易

## 系统组件

### 1. 策略实现
- `quant_trading_system/strategy/ma5_strategy.py` - 5日均线策略实现
  - `MA5Strategy` - Backtrader回测策略类
  - `MA5LiveStrategy` - 实盘交易策略类

### 2. 回测引擎
- `quant_trading_system/backtest/backtest_engine.py` - 回测引擎
  - 使用Backtrader进行历史数据回测
  - 生成详细的回测报告和图表

### 3. 测试脚本
- `test_apple_ma5_strategy.py` - 完整的策略测试脚本
- `test_tiger_connection.py` - Tiger API连接测试脚本

## 配置说明

### Tiger API配置

系统支持使用Tiger Open API配置文件的方式进行认证：

1. 从Tiger开发者网站下载配置文件：https://quant.itigerup.com/#developer
2. 将 `tiger_openapi_config.properties` 文件放入 `config/tiger_props/` 目录
3. 配置文件格式示例：

```properties
tiger_id=your_tiger_id
account=your_account_id
private_key=-----BEGIN RSA PRIVATE KEY-----
your_private_key_content_here
-----END RSA PRIVATE KEY-----
server_url=https://openapi.itiger.com/gateway
socket_host_and_port=openapi.itiger.com:8883
charset=UTF-8
sign_type=RSA
format=json
version=2.0
```

### 系统配置

编辑 `config/config.yaml` 文件：

```yaml
# Tiger API配置 - 使用配置文件方式
tiger:
  props_path: "config/tiger_props/"  # Tiger配置文件路径
  use_config_file: true  # 使用配置文件方式

# 风险管理配置
risk:
  max_drawdown: 0.10
  max_position_size: 0.05
  max_daily_trades: 50
  max_total_exposure: 0.80
```

## 使用方法

### 1. 环境准备

确保安装了所需的依赖包：

```bash
pip install -r requirements.txt
```

### 2. 快速开始

如果你想立即测试系统（无需Tiger API配置），运行：

```bash
python run_apple_ma5_complete_test.py
```

这将运行完整的测试，包括：
- 使用模拟数据的回测
- 策略信号生成测试
- 结果分析和保存

或者运行简化版本：

```bash
python test_ma5_with_sample_data.py
```

### 3. Tiger API连接测试（可选）

如果你有Tiger API账户，首先测试连接：

```bash
python test_tiger_connection.py
```

这个脚本会：
- 测试Tiger API连接
- 获取Apple股票的实时行情
- 获取K线数据
- 测试账户信息获取

### 4. 其他测试脚本

还有其他专门的测试脚本可用：

```bash
# 原始的完整策略测试（需要Tiger API）
python test_apple_ma5_strategy.py

# 使用示例数据的策略测试（无需外部API）
python test_ma5_with_sample_data.py
```

## 输出结果

### 回测结果

回测完成后会生成：
- 控制台输出的详细回测报告
- `results/` 目录下的CSV结果文件
- `results/plots/` 目录下的回测图表

示例回测报告：
```
============================================================
BACKTEST RESULTS
============================================================
Symbol: AAPL
Period: 2023-01-01 to 2024-01-01
Initial Value: $100,000.00
Final Value: $115,230.45
Total Return: 15.23%
Sharpe Ratio: 1.25
Max Drawdown: 8.45%
Total Trades: 24
Winning Trades: 15
Losing Trades: 9
Win Rate: 62.50%
============================================================
```

### 实盘信号

实盘策略测试会生成交易信号文件，包含：
- 信号日期
- 交易动作（BUY/SELL）
- 信号价格
- 5日均线值
- 信号原因

## 文件结构

```
├── config/
│   ├── config.yaml                 # 系统配置文件
│   └── tiger_props/               # Tiger API配置文件目录
│       └── tiger_openapi_config.properties
├── quant_trading_system/
│   ├── strategy/
│   │   └── ma5_strategy.py        # 5日均线策略
│   ├── backtest/
│   │   └── backtest_engine.py     # 回测引擎
│   └── ...
├── results/                       # 结果输出目录
│   ├── plots/                     # 回测图表
│   └── *.csv                      # 结果数据文件
├── logs/                          # 日志文件
├── test_apple_ma5_strategy.py     # 主测试脚本
├── test_tiger_connection.py       # Tiger连接测试
└── README_MA5_Strategy.md         # 本说明文件
```

## 注意事项

1. **Tiger API权限**：确保你的Tiger账户有相应的API权限
2. **网络连接**：需要稳定的网络连接来获取实时数据
3. **数据延迟**：实时数据可能有延迟，实际交易时需要考虑
4. **风险管理**：这是一个测试系统，实际交易前请充分测试和验证
5. **手续费**：回测中设置了0.1%的手续费，实际费率可能不同

## 扩展功能

可以基于这个框架扩展更多功能：
- 添加更多技术指标
- 实现多股票组合策略
- 添加止损止盈功能
- 集成更多数据源
- 实现自动化交易

## 支持

如果遇到问题，请检查：
1. Tiger API配置是否正确
2. 网络连接是否正常
3. 依赖包是否完整安装
4. 日志文件中的错误信息
