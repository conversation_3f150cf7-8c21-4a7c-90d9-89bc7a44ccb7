"""
可视化模块测试
测试图表生成功能
"""

import unittest
import os
import shutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant_trading_system.visualization.chart_generator import ChartGenerator


class TestChartGenerator(unittest.TestCase):
    """图表生成器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.chart_generator = ChartGenerator()
        
        # 创建测试数据
        self._create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_data(self):
        """创建测试数据"""
        # 生成价格数据
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        prices = []
        current_price = 100.0
        for _ in range(len(dates)):
            change = np.random.normal(0, 0.02)
            current_price *= (1 + change)
            prices.append(current_price)
        
        self.price_data = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'Close': prices,
            'Volume': [np.random.randint(1000000, 5000000) for _ in prices]
        })
        
        # 确保价格关系正确
        for i in range(len(self.price_data)):
            high = max(self.price_data.loc[i, 'Open'], self.price_data.loc[i, 'Close'], self.price_data.loc[i, 'High'])
            low = min(self.price_data.loc[i, 'Open'], self.price_data.loc[i, 'Close'], self.price_data.loc[i, 'Low'])
            self.price_data.loc[i, 'High'] = high
            self.price_data.loc[i, 'Low'] = low
        
        # 生成技术指标
        self.indicators = {
            'MA5': self.price_data['Close'].rolling(window=5).mean(),
            'MA20': self.price_data['Close'].rolling(window=20).mean()
        }
        
        # 生成交易信号
        self.signals = [
            {'date': '2023-01-15', 'action': 'BUY', 'price': 98.5},
            {'date': '2023-02-10', 'action': 'SELL', 'price': 105.2},
            {'date': '2023-03-05', 'action': 'BUY', 'price': 102.8}
        ]
        
        # 生成投资组合数据
        portfolio_values = []
        current_value = 100000
        for _ in range(len(dates)):
            change = np.random.normal(0.001, 0.015)  # 平均日收益0.1%，波动1.5%
            current_value *= (1 + change)
            portfolio_values.append(current_value)
        
        self.portfolio_data = pd.DataFrame({
            'Date': dates,
            'Value': portfolio_values
        })
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.chart_generator.output_dir, str)
        self.assertTrue(os.path.exists(self.chart_generator.output_dir))
    
    def test_plot_price_chart(self):
        """测试价格图表绘制"""
        # 测试基本价格图表
        chart_path = self.chart_generator.plot_price_chart(
            data=self.price_data,
            symbol='AAPL'
        )
        
        # 检查文件是否创建
        self.assertTrue(os.path.exists(chart_path))
        self.assertTrue(chart_path.endswith('.png'))
        
        # 测试带技术指标的图表
        chart_path_with_indicators = self.chart_generator.plot_price_chart(
            data=self.price_data,
            symbol='AAPL',
            indicators=self.indicators
        )
        
        self.assertTrue(os.path.exists(chart_path_with_indicators))
        
        # 测试带交易信号的图表
        chart_path_with_signals = self.chart_generator.plot_price_chart(
            data=self.price_data,
            symbol='AAPL',
            indicators=self.indicators,
            signals=self.signals
        )
        
        self.assertTrue(os.path.exists(chart_path_with_signals))
    
    def test_plot_portfolio_performance(self):
        """测试投资组合表现图表"""
        chart_path = self.chart_generator.plot_portfolio_performance(
            portfolio_data=self.portfolio_data
        )
        
        # 检查文件是否创建
        self.assertTrue(os.path.exists(chart_path))
        self.assertTrue(chart_path.endswith('.png'))
        
        # 测试带基准的图表
        benchmark_data = self.portfolio_data.copy()
        benchmark_data['Value'] = benchmark_data['Value'] * 0.95  # 模拟基准表现稍差
        
        chart_path_with_benchmark = self.chart_generator.plot_portfolio_performance(
            portfolio_data=self.portfolio_data,
            benchmark_data=benchmark_data
        )
        
        self.assertTrue(os.path.exists(chart_path_with_benchmark))
    
    def test_plot_risk_metrics(self):
        """测试风险指标图表"""
        # 创建测试风险数据
        risk_data = {
            'var_history': {
                'dates': pd.date_range('2023-01-01', periods=50, freq='D').strftime('%Y-%m-%d').tolist(),
                'var_5pct': np.random.uniform(0.01, 0.05, 50).tolist(),
                'var_1pct': np.random.uniform(0.02, 0.08, 50).tolist()
            },
            'position_concentration': {
                'AAPL': 0.3,
                'MSFT': 0.25,
                'GOOGL': 0.2,
                'AMZN': 0.15,
                'Others': 0.1
            },
            'sector_exposure': {
                'Technology': 45.0,
                'Healthcare': 20.0,
                'Finance': 15.0,
                'Consumer': 12.0,
                'Energy': 8.0
            },
            'risk_metrics': {
                'Max Drawdown': 0.08,
                'Volatility': 0.15,
                'Beta': 1.2,
                'Sharpe Ratio': 1.5
            }
        }
        
        chart_path = self.chart_generator.plot_risk_metrics(risk_data)
        
        # 检查文件是否创建
        self.assertTrue(os.path.exists(chart_path))
        self.assertTrue(chart_path.endswith('.png'))
    
    def test_plot_risk_metrics_partial_data(self):
        """测试部分风险数据的图表"""
        # 只提供部分数据
        partial_risk_data = {
            'position_concentration': {
                'AAPL': 0.4,
                'MSFT': 0.3,
                'Others': 0.3
            },
            'risk_metrics': {
                'Volatility': 0.12,
                'Sharpe Ratio': 1.8
            }
        }
        
        chart_path = self.chart_generator.plot_risk_metrics(partial_risk_data)
        
        # 即使数据不完整，也应该能生成图表
        self.assertTrue(os.path.exists(chart_path))
        self.assertTrue(chart_path.endswith('.png'))
    
    def test_custom_save_path(self):
        """测试自定义保存路径"""
        custom_path = "custom_chart.png"
        
        chart_path = self.chart_generator.plot_price_chart(
            data=self.price_data,
            symbol='AAPL',
            save_path=custom_path
        )
        
        # 检查是否使用了自定义路径
        self.assertEqual(chart_path, custom_path)
        self.assertTrue(os.path.exists(custom_path))
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空数据
        empty_data = pd.DataFrame()
        
        chart_path = self.chart_generator.plot_price_chart(
            data=empty_data,
            symbol='EMPTY'
        )
        
        # 应该返回空字符串表示失败
        self.assertEqual(chart_path, "")
        
        # 测试无效的投资组合数据
        invalid_portfolio_data = pd.DataFrame({'Date': ['invalid'], 'Value': ['invalid']})
        
        chart_path = self.chart_generator.plot_portfolio_performance(
            portfolio_data=invalid_portfolio_data
        )
        
        self.assertEqual(chart_path, "")
    
    def test_create_interactive_chart(self):
        """测试交互式图表创建"""
        # 注意：这个测试可能会因为plotly未安装而跳过
        try:
            chart_path = self.chart_generator.create_interactive_chart(
                data=self.price_data,
                chart_type='line'
            )
            
            # 如果plotly可用，应该创建HTML文件
            if chart_path:
                self.assertTrue(os.path.exists(chart_path))
                self.assertTrue(chart_path.endswith('.html'))
            else:
                # 如果plotly不可用，应该返回空字符串
                self.assertEqual(chart_path, "")
                
        except Exception as e:
            # 如果出现其他错误，记录但不失败测试
            print(f"Interactive chart test skipped: {e}")
    
    def test_multiple_charts_generation(self):
        """测试批量生成图表"""
        chart_paths = []
        
        # 生成多个不同类型的图表
        chart_paths.append(self.chart_generator.plot_price_chart(
            self.price_data, 'AAPL', self.indicators, self.signals
        ))
        
        chart_paths.append(self.chart_generator.plot_portfolio_performance(
            self.portfolio_data
        ))
        
        risk_data = {
            'risk_metrics': {
                'Volatility': 0.15,
                'Sharpe Ratio': 1.2
            }
        }
        chart_paths.append(self.chart_generator.plot_risk_metrics(risk_data))
        
        # 检查所有图表都成功生成
        for path in chart_paths:
            self.assertTrue(os.path.exists(path))
        
        # 检查文件数量
        chart_files = [f for f in os.listdir(self.chart_generator.output_dir) if f.endswith('.png')]
        self.assertGreaterEqual(len(chart_files), 3)


class TestChartGeneratorIntegration(unittest.TestCase):
    """图表生成器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.chart_generator = ChartGenerator()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_chart_generation_workflow(self):
        """测试完整的图表生成工作流"""
        # 创建真实的数据结构
        dates = pd.date_range('2023-01-01', periods=30, freq='D')
        
        # 模拟真实的股票数据
        np.random.seed(42)
        base_price = 100
        prices = []
        for i in range(len(dates)):
            change = np.random.normal(0.001, 0.02)  # 日均涨跌0.1%，波动2%
            base_price *= (1 + change)
            prices.append(base_price)
        
        stock_data = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'Close': prices,
            'Volume': [np.random.randint(1000000, 3000000) for _ in prices]
        })
        
        # 修正价格关系
        for i in range(len(stock_data)):
            high = max(stock_data.loc[i, 'Open'], stock_data.loc[i, 'Close'], stock_data.loc[i, 'High'])
            low = min(stock_data.loc[i, 'Open'], stock_data.loc[i, 'Close'], stock_data.loc[i, 'Low'])
            stock_data.loc[i, 'High'] = high
            stock_data.loc[i, 'Low'] = low
        
        # 生成技术指标
        indicators = {
            'MA5': stock_data['Close'].rolling(window=5).mean(),
            'MA10': stock_data['Close'].rolling(window=10).mean()
        }
        
        # 生成交易信号
        signals = []
        for i in range(5, len(stock_data), 5):  # 每5天一个信号
            action = 'BUY' if i % 10 == 5 else 'SELL'
            signals.append({
                'date': stock_data.loc[i, 'Date'].strftime('%Y-%m-%d'),
                'action': action,
                'price': stock_data.loc[i, 'Close']
            })
        
        # 生成价格图表
        price_chart = self.chart_generator.plot_price_chart(
            data=stock_data,
            symbol='TEST',
            indicators=indicators,
            signals=signals
        )
        
        # 验证图表生成
        self.assertTrue(os.path.exists(price_chart))
        
        # 生成投资组合数据
        portfolio_data = pd.DataFrame({
            'Date': dates,
            'Value': np.cumsum(np.random.normal(100, 500, len(dates))) + 100000
        })
        
        # 生成投资组合图表
        portfolio_chart = self.chart_generator.plot_portfolio_performance(portfolio_data)
        
        # 验证图表生成
        self.assertTrue(os.path.exists(portfolio_chart))
        
        # 检查输出目录
        chart_files = os.listdir(self.chart_generator.output_dir)
        png_files = [f for f in chart_files if f.endswith('.png')]
        
        # 应该至少有2个PNG文件
        self.assertGreaterEqual(len(png_files), 2)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
