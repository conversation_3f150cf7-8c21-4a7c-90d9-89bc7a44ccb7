"""
交易分析模块
计算收益率、最大回撤、夏普比率等指标，并生成报告和图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 可选依赖
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import os
from pathlib import Path
import json

from .trade_recorder import TradeRecorder


class TradeAnalyzer:
    """
    交易分析器
    
    功能特性:
    1. 计算各种交易指标
    2. 生成详细的分析报告
    3. 创建可视化图表
    4. 支持多策略对比分析
    """
    
    def __init__(self, trade_recorder: TradeRecorder, reports_dir: str = "analysis_reports"):
        """
        初始化交易分析器
        
        Args:
            trade_recorder: 交易记录器
            reports_dir: 报告保存目录
        """
        self.trade_recorder = trade_recorder
        self.reports_dir = reports_dir
        self.logger = logging.getLogger(__name__)
        
        # 创建报告目录
        os.makedirs(self.reports_dir, exist_ok=True)
        os.makedirs(f"{self.reports_dir}/charts", exist_ok=True)
        
        # 设置图表样式
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('default')

        if HAS_SEABORN:
            sns.set_palette("husl")
        
        self.logger.info("交易分析器初始化完成")
    
    def calculate_performance_metrics(self, trades_df: pd.DataFrame, 
                                    initial_capital: float = 100000) -> Dict[str, Any]:
        """
        计算交易表现指标
        
        Args:
            trades_df: 交易记录DataFrame
            initial_capital: 初始资金
            
        Returns:
            指标字典
        """
        try:
            if trades_df.empty:
                return {}
            
            # 基本统计
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['net_pnl'] > 0])
            losing_trades = len(trades_df[trades_df['net_pnl'] < 0])
            
            # 胜率
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # 盈亏统计
            total_pnl = trades_df['net_pnl'].sum()
            avg_win = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
            
            # 盈亏比
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
            
            # 最大单笔盈利和亏损
            max_win = trades_df['net_pnl'].max()
            max_loss = trades_df['net_pnl'].min()
            
            # 总收益率
            total_return = (total_pnl / initial_capital) * 100
            
            # 计算累积收益曲线
            trades_df_sorted = trades_df.sort_values('exit_time')
            cumulative_pnl = trades_df_sorted['net_pnl'].cumsum()
            cumulative_returns = (cumulative_pnl / initial_capital) * 100
            
            # 最大回撤
            peak = cumulative_returns.expanding().max()
            drawdown = cumulative_returns - peak
            max_drawdown = drawdown.min()
            
            # 夏普比率（简化计算，假设无风险利率为0）
            if len(trades_df) > 1:
                returns_std = trades_df['pnl_percent'].std()
                avg_return = trades_df['pnl_percent'].mean()
                sharpe_ratio = avg_return / returns_std if returns_std != 0 else 0
            else:
                sharpe_ratio = 0
            
            # 平均持仓时间
            avg_holding_period = trades_df['holding_period'].mean()
            
            # 交易频率（每月交易次数）
            if not trades_df.empty:
                trades_df['exit_time'] = pd.to_datetime(trades_df['exit_time'])
                date_range = (trades_df['exit_time'].max() - trades_df['exit_time'].min()).days
                monthly_trades = (total_trades / max(date_range, 1)) * 30
            else:
                monthly_trades = 0
            
            # 盈利因子
            gross_profit = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].sum()
            gross_loss = abs(trades_df[trades_df['net_pnl'] < 0]['net_pnl'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss != 0 else 0
            
            metrics = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'total_return': total_return,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_loss_ratio': profit_loss_ratio,
                'max_win': max_win,
                'max_loss': max_loss,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'avg_holding_period': avg_holding_period,
                'monthly_trades': monthly_trades,
                'profit_factor': profit_factor,
                'gross_profit': gross_profit,
                'gross_loss': gross_loss,
                'cumulative_returns': cumulative_returns.tolist(),
                'analysis_date': datetime.now().isoformat()
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算交易指标失败: {e}")
            return {}
    
    def analyze_strategy_performance(self, strategy: str = None, 
                                   symbol: str = None, 
                                   start_date: str = None, 
                                   end_date: str = None) -> Dict[str, Any]:
        """
        分析策略表现
        
        Args:
            strategy: 策略名称
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            分析结果
        """
        try:
            # 获取交易记录
            trades_df = self.trade_recorder.get_trades(symbol, strategy, start_date, end_date)
            
            if trades_df.empty:
                self.logger.warning("未找到匹配的交易记录")
                return {}
            
            # 计算指标
            metrics = self.calculate_performance_metrics(trades_df)
            
            # 添加额外分析
            analysis = {
                'basic_metrics': metrics,
                'symbol_analysis': self._analyze_by_symbol(trades_df),
                'time_analysis': self._analyze_by_time(trades_df),
                'trade_distribution': self._analyze_trade_distribution(trades_df)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析策略表现失败: {e}")
            return {}
    
    def _analyze_by_symbol(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """按股票分析"""
        try:
            symbol_stats = {}
            
            for symbol in trades_df['symbol'].unique():
                symbol_trades = trades_df[trades_df['symbol'] == symbol]
                symbol_stats[symbol] = {
                    'total_trades': len(symbol_trades),
                    'total_pnl': symbol_trades['net_pnl'].sum(),
                    'win_rate': (len(symbol_trades[symbol_trades['net_pnl'] > 0]) / len(symbol_trades)) * 100,
                    'avg_pnl': symbol_trades['net_pnl'].mean()
                }
            
            return symbol_stats
            
        except Exception as e:
            self.logger.error(f"按股票分析失败: {e}")
            return {}
    
    def _analyze_by_time(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """按时间分析"""
        try:
            trades_df['exit_time'] = pd.to_datetime(trades_df['exit_time'])
            trades_df['month'] = trades_df['exit_time'].dt.to_period('M')
            trades_df['weekday'] = trades_df['exit_time'].dt.day_name()
            trades_df['hour'] = trades_df['exit_time'].dt.hour
            
            time_stats = {
                'monthly_pnl': trades_df.groupby('month')['net_pnl'].sum().to_dict(),
                'weekday_pnl': trades_df.groupby('weekday')['net_pnl'].mean().to_dict(),
                'hourly_pnl': trades_df.groupby('hour')['net_pnl'].mean().to_dict()
            }
            
            return time_stats
            
        except Exception as e:
            self.logger.error(f"按时间分析失败: {e}")
            return {}
    
    def _analyze_trade_distribution(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析交易分布"""
        try:
            # PnL分布
            pnl_bins = [-float('inf'), -1000, -500, -100, 0, 100, 500, 1000, float('inf')]
            pnl_labels = ['<-1000', '-1000~-500', '-500~-100', '-100~0', '0~100', '100~500', '500~1000', '>1000']
            trades_df['pnl_bin'] = pd.cut(trades_df['net_pnl'], bins=pnl_bins, labels=pnl_labels)
            
            # 持仓时间分布
            holding_bins = [0, 1, 4, 24, 168, float('inf')]  # 小时
            holding_labels = ['<1h', '1-4h', '4-24h', '1-7d', '>7d']
            trades_df['holding_bin'] = pd.cut(trades_df['holding_period'], bins=holding_bins, labels=holding_labels)
            
            distribution = {
                'pnl_distribution': trades_df['pnl_bin'].value_counts().to_dict(),
                'holding_distribution': trades_df['holding_bin'].value_counts().to_dict()
            }
            
            return distribution
            
        except Exception as e:
            self.logger.error(f"分析交易分布失败: {e}")
            return {}
    
    def generate_performance_report(self, strategy: str = None, 
                                  symbol: str = None,
                                  save_charts: bool = True) -> str:
        """
        生成交易表现报告
        
        Args:
            strategy: 策略名称
            symbol: 股票代码
            save_charts: 是否保存图表
            
        Returns:
            报告文件路径
        """
        try:
            # 分析交易表现
            analysis = self.analyze_strategy_performance(strategy, symbol)
            
            if not analysis:
                self.logger.warning("无法生成报告：没有分析数据")
                return ""
            
            # 生成报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_name = f"performance_report_{strategy or 'all'}_{symbol or 'all'}_{timestamp}"
            
            # 保存JSON报告
            json_file = f"{self.reports_dir}/{report_name}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False, default=str)
            
            # 生成图表
            if save_charts:
                self._create_performance_charts(analysis, report_name)
            
            # 生成HTML报告
            html_file = self._generate_html_report(analysis, report_name)
            
            self.logger.info(f"交易报告已生成: {html_file}")
            return html_file
            
        except Exception as e:
            self.logger.error(f"生成交易报告失败: {e}")
            return ""
    
    def _create_performance_charts(self, analysis: Dict[str, Any], report_name: str):
        """创建表现图表"""
        try:
            metrics = analysis.get('basic_metrics', {})
            
            # 创建子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Trading Performance Analysis', fontsize=16)
            
            # 1. 累积收益曲线
            if 'cumulative_returns' in metrics:
                axes[0, 0].plot(metrics['cumulative_returns'])
                axes[0, 0].set_title('Cumulative Returns (%)')
                axes[0, 0].set_xlabel('Trade Number')
                axes[0, 0].set_ylabel('Return (%)')
                axes[0, 0].grid(True)
            
            # 2. 盈亏分布
            symbol_analysis = analysis.get('symbol_analysis', {})
            if symbol_analysis:
                symbols = list(symbol_analysis.keys())
                pnls = [symbol_analysis[s]['total_pnl'] for s in symbols]
                axes[0, 1].bar(symbols, pnls)
                axes[0, 1].set_title('PnL by Symbol')
                axes[0, 1].set_ylabel('Total PnL')
                axes[0, 1].tick_params(axis='x', rotation=45)
            
            # 3. 月度收益
            time_analysis = analysis.get('time_analysis', {})
            monthly_pnl = time_analysis.get('monthly_pnl', {})
            if monthly_pnl:
                months = list(monthly_pnl.keys())
                pnls = list(monthly_pnl.values())
                axes[1, 0].bar(range(len(months)), pnls)
                axes[1, 0].set_title('Monthly PnL')
                axes[1, 0].set_ylabel('PnL')
                axes[1, 0].set_xticks(range(len(months)))
                axes[1, 0].set_xticklabels([str(m) for m in months], rotation=45)
            
            # 4. 关键指标
            key_metrics = {
                'Win Rate': f"{metrics.get('win_rate', 0):.1f}%",
                'Total Return': f"{metrics.get('total_return', 0):.1f}%",
                'Sharpe Ratio': f"{metrics.get('sharpe_ratio', 0):.2f}",
                'Max Drawdown': f"{metrics.get('max_drawdown', 0):.1f}%",
                'Profit Factor': f"{metrics.get('profit_factor', 0):.2f}"
            }
            
            axes[1, 1].axis('off')
            y_pos = 0.9
            for metric, value in key_metrics.items():
                axes[1, 1].text(0.1, y_pos, f"{metric}: {value}", 
                               fontsize=12, transform=axes[1, 1].transAxes)
                y_pos -= 0.15
            axes[1, 1].set_title('Key Metrics')
            
            plt.tight_layout()
            
            # 保存图表
            chart_file = f"{self.reports_dir}/charts/{report_name}.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"图表已保存: {chart_file}")
            
        except Exception as e:
            self.logger.error(f"创建图表失败: {e}")
    
    def _generate_html_report(self, analysis: Dict[str, Any], report_name: str) -> str:
        """生成HTML报告"""
        try:
            metrics = analysis.get('basic_metrics', {})
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Trading Performance Report</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 20px; text-align: center; }}
                    .metrics {{ display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }}
                    .metric-box {{ border: 1px solid #ddd; padding: 15px; text-align: center; }}
                    .metric-value {{ font-size: 24px; font-weight: bold; color: #333; }}
                    .metric-label {{ font-size: 14px; color: #666; }}
                    .chart {{ text-align: center; margin: 20px 0; }}
                    .positive {{ color: green; }}
                    .negative {{ color: red; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Trading Performance Report</h1>
                    <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-value {'positive' if metrics.get('total_return', 0) > 0 else 'negative'}">
                            {metrics.get('total_return', 0):.2f}%
                        </div>
                        <div class="metric-label">Total Return</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-value">{metrics.get('win_rate', 0):.1f}%</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-value">{metrics.get('total_trades', 0)}</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-value">{metrics.get('sharpe_ratio', 0):.2f}</div>
                        <div class="metric-label">Sharpe Ratio</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-value negative">{metrics.get('max_drawdown', 0):.2f}%</div>
                        <div class="metric-label">Max Drawdown</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-value">{metrics.get('profit_factor', 0):.2f}</div>
                        <div class="metric-label">Profit Factor</div>
                    </div>
                </div>
                
                <div class="chart">
                    <h2>Performance Chart</h2>
                    <img src="charts/{report_name}.png" alt="Performance Chart" style="max-width: 100%;">
                </div>
                
                <div>
                    <h2>Detailed Metrics</h2>
                    <ul>
                        <li>Average Win: ${metrics.get('avg_win', 0):.2f}</li>
                        <li>Average Loss: ${metrics.get('avg_loss', 0):.2f}</li>
                        <li>Profit/Loss Ratio: {metrics.get('profit_loss_ratio', 0):.2f}</li>
                        <li>Average Holding Period: {metrics.get('avg_holding_period', 0):.1f} hours</li>
                        <li>Monthly Trade Frequency: {metrics.get('monthly_trades', 0):.1f}</li>
                    </ul>
                </div>
            </body>
            </html>
            """
            
            html_file = f"{self.reports_dir}/{report_name}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_file
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            return ""
