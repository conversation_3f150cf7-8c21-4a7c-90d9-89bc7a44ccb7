"""
高级回测引擎测试
测试多策略、多周期、多股票、多参数回测功能
"""

import unittest
import os
import shutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import tempfile
import backtrader as bt

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant_trading_system.data.data_manager import DataManager
from quant_trading_system.backtest.advanced_backtest_engine import AdvancedBacktestEngine
from quant_trading_system.strategy.ma5_strategy import MA5Strategy


class TestStrategy1(bt.Strategy):
    """测试策略1 - 简单买入持有"""
    
    params = (
        ('period', 10),
    )
    
    def __init__(self):
        self.order = None
        self.bought = False
    
    def next(self):
        if not self.bought and len(self.data) > self.params.period:
            self.order = self.buy()
            self.bought = True


class TestStrategy2(bt.Strategy):
    """测试策略2 - 简单移动平均"""
    
    params = (
        ('ma_period', 20),
    )
    
    def __init__(self):
        self.ma = bt.indicators.SimpleMovingAverage(self.data.close, period=self.params.ma_period)
        self.order = None
    
    def next(self):
        if self.order:
            return
        
        if not self.position:
            if self.data.close[0] > self.ma[0]:
                self.order = self.buy()
        else:
            if self.data.close[0] < self.ma[0]:
                self.order = self.sell()


class TestAdvancedBacktestEngine(unittest.TestCase):
    """高级回测引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建数据管理器和回测引擎
        self.data_manager = DataManager(use_csv=True)
        self.backtest_engine = AdvancedBacktestEngine(
            data_manager=self.data_manager,
            initial_cash=100000,
            commission=0.001
        )
        
        # 创建测试数据
        self._create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_data(self):
        """创建测试数据"""
        # 生成测试股票数据
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        timeframes = ['1d', '1h']
        
        for symbol in symbols:
            for timeframe in timeframes:
                # 生成随机价格数据
                np.random.seed(42)  # 确保可重复
                dates = pd.date_range('2023-01-01', periods=100, freq='D' if timeframe == '1d' else 'H')
                
                # 生成价格序列
                prices = []
                current_price = 100.0
                for _ in range(len(dates)):
                    change = np.random.normal(0, 0.02)
                    current_price *= (1 + change)
                    prices.append(current_price)
                
                # 创建OHLCV数据
                data = pd.DataFrame({
                    'Date': dates,
                    'Open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
                    'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                    'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                    'Close': prices,
                    'Volume': [np.random.randint(1000000, 5000000) for _ in prices],
                    'Symbol': [symbol] * len(dates)
                })
                
                # 确保价格关系正确
                for i in range(len(data)):
                    high = max(data.loc[i, 'Open'], data.loc[i, 'Close'], data.loc[i, 'High'])
                    low = min(data.loc[i, 'Open'], data.loc[i, 'Close'], data.loc[i, 'Low'])
                    data.loc[i, 'High'] = high
                    data.loc[i, 'Low'] = low
                
                # 保存测试数据
                self.data_manager._save_to_csv(data, symbol, timeframe)
    
    def test_single_strategy_backtest(self):
        """测试单策略回测"""
        result = self.backtest_engine.single_strategy_backtest(
            strategy_class=TestStrategy1,
            symbols=['AAPL'],
            timeframe='1d',
            strategy_params={'period': 5}
        )
        
        # 检查结果
        self.assertIsInstance(result, dict)
        self.assertIn('strategy_name', result)
        self.assertIn('total_return', result)
        self.assertIn('sharpe_ratio', result)
        self.assertIn('max_drawdown', result)
        self.assertEqual(result['strategy_name'], 'TestStrategy1')
        self.assertEqual(result['symbols'], ['AAPL'])
        self.assertEqual(result['timeframe'], '1d')
    
    def test_multi_strategy_backtest(self):
        """测试多策略回测"""
        strategy_configs = [
            {'class': TestStrategy1, 'params': {'period': 5}},
            {'class': TestStrategy2, 'params': {'ma_period': 10}}
        ]
        
        result = self.backtest_engine.multi_strategy_backtest(
            strategy_configs=strategy_configs,
            symbols=['AAPL'],
            timeframe='1d'
        )
        
        # 检查结果
        self.assertIsInstance(result, dict)
        self.assertIn('individual_results', result)
        self.assertIn('comparison', result)
        
        individual_results = result['individual_results']
        self.assertEqual(len(individual_results), 2)
        
        # 检查策略名称
        strategy_names = list(individual_results.keys())
        self.assertIn('TestStrategy1_0', strategy_names)
        self.assertIn('TestStrategy2_1', strategy_names)
    
    def test_multi_timeframe_backtest(self):
        """测试多时间周期回测"""
        result = self.backtest_engine.multi_timeframe_backtest(
            strategy_class=TestStrategy1,
            symbols=['AAPL'],
            timeframes=['1d', '1h'],
            strategy_params={'period': 5}
        )
        
        # 检查结果
        self.assertIsInstance(result, dict)
        self.assertIn('individual_results', result)
        self.assertIn('comparison', result)
        
        individual_results = result['individual_results']
        self.assertEqual(len(individual_results), 2)
        self.assertIn('1d', individual_results)
        self.assertIn('1h', individual_results)
    
    def test_multi_symbol_backtest(self):
        """测试多股票回测"""
        symbol_groups = [
            ['AAPL'],
            ['MSFT'],
            ['AAPL', 'MSFT']
        ]
        
        result = self.backtest_engine.multi_symbol_backtest(
            strategy_class=TestStrategy1,
            symbol_groups=symbol_groups,
            timeframe='1d',
            strategy_params={'period': 5}
        )
        
        # 检查结果
        self.assertIsInstance(result, dict)
        self.assertIn('individual_results', result)
        self.assertIn('comparison', result)
        
        individual_results = result['individual_results']
        self.assertEqual(len(individual_results), 3)
    
    def test_parameter_optimization_backtest(self):
        """测试参数优化回测"""
        param_ranges = {
            'period': [5, 10, 15],
        }
        
        result = self.backtest_engine.parameter_optimization_backtest(
            strategy_class=TestStrategy1,
            symbols=['AAPL'],
            param_ranges=param_ranges,
            timeframe='1d',
            max_workers=2,
            optimization_target='total_return'
        )
        
        # 检查结果
        self.assertIsInstance(result, dict)
        self.assertIn('best_result', result)
        self.assertIn('all_results', result)
        self.assertIn('optimization_report', result)
        
        all_results = result['all_results']
        self.assertEqual(len(all_results), 3)  # 3个参数组合
        
        # 检查最佳结果
        best_result = result['best_result']
        self.assertIn('params', best_result)
        self.assertIn('period', best_result['params'])
    
    def test_prepare_data(self):
        """测试数据准备"""
        data = self.backtest_engine._prepare_data('AAPL', '1d')
        
        # 检查数据
        self.assertIsInstance(data, pd.DataFrame)
        self.assertGreater(len(data), 0)
        
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            self.assertIn(col, data.columns)
    
    def test_convert_to_bt_data(self):
        """测试Backtrader数据转换"""
        # 准备测试数据
        data = self.backtest_engine._prepare_data('AAPL', '1d')
        
        if not data.empty:
            bt_data = self.backtest_engine._convert_to_bt_data(data, 'AAPL')
            self.assertIsInstance(bt_data, bt.feeds.PandasData)
    
    def test_process_results(self):
        """测试结果处理"""
        # 这个测试需要实际的策略结果，比较复杂
        # 我们主要测试方法是否存在和基本功能
        self.assertTrue(hasattr(self.backtest_engine, '_process_results'))
        self.assertTrue(callable(getattr(self.backtest_engine, '_process_results')))
    
    def test_generate_reports(self):
        """测试报告生成"""
        # 测试策略比较报告
        mock_results = {
            'strategy1': {
                'total_return': 10.5,
                'sharpe_ratio': 1.2,
                'max_drawdown': -5.0,
                'win_rate': 60.0,
                'total_trades': 20
            },
            'strategy2': {
                'total_return': 8.3,
                'sharpe_ratio': 0.9,
                'max_drawdown': -3.0,
                'win_rate': 55.0,
                'total_trades': 15
            }
        }
        
        comparison = self.backtest_engine._generate_strategy_comparison(mock_results)
        
        # 检查比较报告
        self.assertIsInstance(comparison, dict)
        self.assertIn('rankings', comparison)
        self.assertIn('statistics', comparison)
        self.assertIn('overall_ranking', comparison)
    
    def test_save_backtest_result(self):
        """测试结果保存"""
        test_result = {
            'strategy_name': 'TestStrategy',
            'total_return': 10.0,
            'test_data': True
        }
        
        # 保存结果
        self.backtest_engine._save_backtest_result(test_result, 'test')
        
        # 检查文件是否创建
        reports_dir = f"{self.backtest_engine.results_dir}/reports"
        self.assertTrue(os.path.exists(reports_dir))
        
        # 检查是否有JSON文件创建
        json_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
        self.assertGreater(len(json_files), 0)


class TestAdvancedBacktestEngineIntegration(unittest.TestCase):
    """高级回测引擎集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.data_manager = DataManager(use_csv=True)
        self.backtest_engine = AdvancedBacktestEngine(self.data_manager)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_full_backtest_workflow(self):
        """测试完整回测工作流"""
        # 注意：这个测试需要实际数据，在没有网络的环境中可能失败
        try:
            # 尝试下载一些测试数据
            data = self.data_manager.download_stock_data('AAPL', '1d', period='1mo')
            
            if not data.empty:
                # 运行单策略回测
                result = self.backtest_engine.single_strategy_backtest(
                    strategy_class=MA5Strategy,
                    symbols=['AAPL'],
                    timeframe='1d',
                    strategy_params={'ma_period': 5}
                )
                
                # 检查结果
                self.assertIsInstance(result, dict)
                self.assertIn('total_return', result)
                
                # 检查结果文件是否生成
                reports_dir = f"{self.backtest_engine.results_dir}/reports"
                if os.path.exists(reports_dir):
                    json_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
                    self.assertGreater(len(json_files), 0)
            else:
                self.skipTest("无法下载测试数据")
                
        except Exception as e:
            self.skipTest(f"集成测试跳过: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
