2025-07-28 21:29:08,800 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:29:09,172 - __main__ - ERROR - 系统异常: private key can not be empty
Traceback (most recent call last):
  File "E:\Code\tiget-project\main.py", line 68, in main
    tiger_client = create_tiger_client(config)
  File "E:\Code\tiget-project\main.py", line 43, in create_tiger_client
    return TigerOpenClient(client_config)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 44, in __init__
    raise Exception('private key can not be empty')
Exception: private key can not be empty
2025-07-28 21:30:00,552 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:30:00,943 - __main__ - ERROR - 系统异常: 'TigerOpenClient' object has no attribute '_TigerOpenClient__logger'
Traceback (most recent call last):
  File "E:\Code\tiget-project\main.py", line 68, in main
    tiger_client = create_tiger_client(config)
  File "E:\Code\tiget-project\main.py", line 43, in create_tiger_client
    return TigerOpenClient(client_config)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 61, in __init__
    self._initialize()
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 65, in _initialize
    self.__logger.info(f'sdk version: {self.__config.sdk_version}')
AttributeError: 'TigerOpenClient' object has no attribute '_TigerOpenClient__logger'
2025-07-28 21:33:15,055 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:33:15,434 - __main__ - WARNING - Tiger客户端创建失败: 'TigerOpenClient' object has no attribute '_TigerOpenClient__logger'
2025-07-28 21:33:15,434 - __main__ - INFO - 将在无Tiger API的模式下运行
2025-07-28 21:33:15,501 - __main__ - INFO - Tiger客户端不可用，跳过实盘交易器创建
2025-07-28 21:33:15,502 - __main__ - INFO - 测试历史数据获取...
2025-07-28 21:33:16,209 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-28 21:33:16,706 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-28 21:33:17,149 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-28 21:33:17,149 - __main__ - INFO - Tiger客户端不可用，跳过实时行情测试
2025-07-28 21:33:17,149 - __main__ - INFO - 系统初始化完成
2025-07-28 21:34:12,659 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:34:13,062 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:34:13,062 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:34:13,062 - __main__ - ERROR - 系统异常: 'TigerOpenClient' object has no attribute 'private_key'
Traceback (most recent call last):
  File "E:\Code\tiget-project\main.py", line 100, in main
    data_manager = DataManager(tiger_client)
  File "E:\Code\tiget-project\quant_trading_system\data\data_manager.py", line 14, in __init__
    self.quote_client = QuoteClient(tiger_client) if tiger_client else None
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\quote\quote_client.py", line 87, in __init__
    super(QuoteClient, self).__init__(client_config, logger=logger)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 43, in __init__
    if not client_config.private_key:
AttributeError: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:34:48,039 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:34:48,462 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:34:48,462 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:34:48,462 - __main__ - ERROR - 系统异常: 'TigerOpenClient' object has no attribute 'private_key'
Traceback (most recent call last):
  File "E:\Code\tiget-project\main.py", line 105, in main
    data_manager = DataManager(tiger_client)
  File "E:\Code\tiget-project\quant_trading_system\data\data_manager.py", line 14, in __init__
    self.quote_client = QuoteClient(tiger_client) if tiger_client else None
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\quote\quote_client.py", line 87, in __init__
    super(QuoteClient, self).__init__(client_config, logger=logger)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 43, in __init__
    if not client_config.private_key:
AttributeError: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:35:21,894 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:35:22,403 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:35:22,403 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:35:22,403 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:35:22,404 - __main__ - ERROR - 系统异常: 'TigerOpenClient' object has no attribute 'private_key'
Traceback (most recent call last):
  File "E:\Code\tiget-project\main.py", line 112, in main
    live_trader = LiveTrader(
  File "E:\Code\tiget-project\quant_trading_system\trading\live_trader.py", line 15, in __init__
    self.trade_client = TradeClient(tiger_client)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\trade\trade_client.py", line 43, in __init__
    super(TradeClient, self).__init__(client_config, logger=logger)
  File "C:\Users\<USER>\miniconda3\envs\tiger\lib\site-packages\tigeropen\tiger_open_client.py", line 43, in __init__
    if not client_config.private_key:
AttributeError: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:36:55,616 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:36:55,984 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:36:55,984 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:36:55,984 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:36:55,984 - quant_trading_system.trading.live_trader - WARNING - TradeClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:36:55,984 - __main__ - INFO - 实盘交易器创建成功
2025-07-28 21:36:55,984 - __main__ - INFO - 测试历史数据获取...
2025-07-28 21:36:56,751 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-28 21:36:58,035 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-28 21:36:58,767 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-28 21:36:58,767 - __main__ - INFO - 测试实时行情获取...
2025-07-28 21:36:58,767 - __main__ - WARNING - 实时行情获取失败: TigerOpen client not configured
2025-07-28 21:36:58,767 - __main__ - INFO - 系统初始化完成
2025-07-28 21:38:23,662 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:38:24,041 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:38:24,041 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:38:24,041 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:38:24,043 - quant_trading_system.trading.live_trader - WARNING - TradeClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:38:24,043 - __main__ - INFO - 实盘交易器创建成功
2025-07-28 21:38:24,043 - __main__ - INFO - 测试历史数据获取...
2025-07-28 21:38:24,492 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-28 21:38:25,168 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-28 21:38:26,317 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-28 21:38:26,317 - __main__ - INFO - 测试实时行情获取...
2025-07-28 21:38:26,317 - __main__ - WARNING - 实时行情获取失败: TigerOpen client not configured
2025-07-28 21:38:26,317 - __main__ - INFO - 系统初始化完成
2025-07-28 21:41:37,060 - __main__ - INFO - 量化交易系统启动
2025-07-28 21:41:37,460 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-28 21:41:37,460 - __main__ - INFO - Tiger客户端创建成功
2025-07-28 21:41:37,460 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:41:37,460 - quant_trading_system.trading.live_trader - WARNING - TradeClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-28 21:41:37,460 - __main__ - INFO - 实盘交易器创建成功
2025-07-28 21:41:37,460 - __main__ - INFO - 测试历史数据获取...
2025-07-28 21:41:37,940 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-28 21:41:38,428 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-28 21:41:38,871 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-28 21:41:38,871 - __main__ - INFO - 测试实时行情获取...
2025-07-28 21:41:38,871 - __main__ - WARNING - 实时行情获取失败: TigerOpen client not configured
2025-07-28 21:41:38,871 - __main__ - INFO - 系统初始化完成
2025-07-29 09:57:40,252 - __main__ - INFO - 量化交易系统启动
2025-07-29 09:57:40,620 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-29 09:57:40,620 - __main__ - INFO - Tiger客户端创建成功
2025-07-29 09:57:40,626 - quant_trading_system.data.data_manager - INFO - 数据目录初始化完成
2025-07-29 09:57:40,626 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-29 09:57:40,626 - quant_trading_system.risk.risk_manager - INFO - 高级风险管理器初始化完成
2025-07-29 09:57:40,626 - quant_trading_system.trading.trade_recorder - INFO - 记录文件初始化完成
2025-07-29 09:57:40,637 - quant_trading_system.trading.trade_recorder - INFO - 交易记录器初始化完成
2025-07-29 09:57:40,637 - quant_trading_system.trading.live_trader - WARNING - TradeClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-29 09:57:40,637 - __main__ - INFO - 实盘交易器创建成功
2025-07-29 09:57:40,637 - __main__ - INFO - 测试历史数据获取...
2025-07-29 09:57:40,637 - quant_trading_system.data.data_manager - INFO - 开始下载 AAPL 1d 数据
2025-07-29 09:57:41,075 - quant_trading_system.data.data_manager - WARNING - 未获取到 AAPL 的数据
2025-07-29 09:57:41,075 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-29 09:57:41,076 - quant_trading_system.data.data_manager - INFO - 开始下载 MSFT 1d 数据
2025-07-29 09:57:41,553 - quant_trading_system.data.data_manager - WARNING - 未获取到 MSFT 的数据
2025-07-29 09:57:41,553 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-29 09:57:41,553 - quant_trading_system.data.data_manager - INFO - 开始下载 GOOGL 1d 数据
2025-07-29 09:57:41,981 - quant_trading_system.data.data_manager - WARNING - 未获取到 GOOGL 的数据
2025-07-29 09:57:41,981 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-29 09:57:41,981 - __main__ - INFO - 测试实时行情获取...
2025-07-29 09:57:41,981 - __main__ - WARNING - 实时行情获取失败: TigerOpen client not configured
2025-07-29 09:57:41,981 - __main__ - INFO - 系统初始化完成
2025-07-30 15:22:48,214 - __main__ - INFO - 量化交易系统启动
2025-07-30 15:22:48,717 - tigeropen.tiger_open_client - INFO - sdk version: 2.4.0
2025-07-30 15:22:48,717 - __main__ - INFO - Tiger客户端创建成功
2025-07-30 15:22:48,717 - quant_trading_system.data.data_manager - INFO - 数据目录初始化完成
2025-07-30 15:22:48,717 - quant_trading_system.data.data_manager - WARNING - QuoteClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-30 15:22:48,717 - quant_trading_system.risk.risk_manager - INFO - 高级风险管理器初始化完成
2025-07-30 15:22:48,717 - quant_trading_system.trading.trade_recorder - INFO - 记录文件初始化完成
2025-07-30 15:22:48,754 - quant_trading_system.trading.trade_recorder - INFO - 交易记录器初始化完成
2025-07-30 15:22:48,754 - quant_trading_system.trading.live_trader - WARNING - TradeClient初始化失败: 'TigerOpenClient' object has no attribute 'private_key'
2025-07-30 15:22:48,754 - __main__ - INFO - 实盘交易器创建成功
2025-07-30 15:22:48,754 - __main__ - INFO - 测试历史数据获取...
2025-07-30 15:22:48,754 - quant_trading_system.data.data_manager - INFO - 开始下载 AAPL 1d 数据
2025-07-30 15:22:49,411 - quant_trading_system.data.data_manager - WARNING - 未获取到 AAPL 的数据
2025-07-30 15:22:49,412 - __main__ - INFO - AAPL 数据获取完成，共 0 条记录
2025-07-30 15:22:49,412 - quant_trading_system.data.data_manager - INFO - 开始下载 MSFT 1d 数据
2025-07-30 15:22:49,986 - quant_trading_system.data.data_manager - WARNING - 未获取到 MSFT 的数据
2025-07-30 15:22:49,986 - __main__ - INFO - MSFT 数据获取完成，共 0 条记录
2025-07-30 15:22:49,986 - quant_trading_system.data.data_manager - INFO - 开始下载 GOOGL 1d 数据
2025-07-30 15:22:50,577 - quant_trading_system.data.data_manager - WARNING - 未获取到 GOOGL 的数据
2025-07-30 15:22:50,577 - __main__ - INFO - GOOGL 数据获取完成，共 0 条记录
2025-07-30 15:22:50,577 - __main__ - INFO - 测试实时行情获取...
2025-07-30 15:22:50,577 - __main__ - WARNING - 实时行情获取失败: TigerOpen client not configured
2025-07-30 15:22:50,577 - __main__ - INFO - 系统初始化完成
