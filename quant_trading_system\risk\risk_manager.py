from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import json
import os

@dataclass
class RiskLimits:
    """风险限制配置"""
    max_drawdown: float = 0.1  # 最大回撤10%
    max_position_size: float = 0.05  # 单个持仓最大5%
    max_daily_trades: int = 50
    max_total_exposure: float = 0.8  # 总敞口80%
    max_correlation: float = 0.7  # 最大相关性
    max_sector_exposure: float = 0.3  # 单个行业最大敞口30%
    stop_loss_pct: float = 0.05  # 止损比例5%
    take_profit_pct: float = 0.15  # 止盈比例15%
    max_leverage: float = 1.0  # 最大杠杆
    var_limit: float = 0.02  # VaR限制2%
    stress_test_loss_limit: float = 0.15  # 压力测试损失限制15%

    # 动态风险控制
    volatility_threshold: float = 0.3  # 波动率阈值
    liquidity_threshold: float = 1000000  # 流动性阈值（美元）

    # 时间相关限制
    trading_hours: Dict[str, str] = field(default_factory=lambda: {
        'start': '09:30',
        'end': '16:00'
    })

    # 黑名单和白名单
    blacklist: List[str] = field(default_factory=list)
    whitelist: List[str] = field(default_factory=list)

class RiskManager:
    """
    高级风险管理器

    功能特性:
    1. 多维度风险控制
    2. 动态风险调整
    3. 实时风险监控
    4. 风险报告生成
    5. 压力测试
    """

    def __init__(self, limits: RiskLimits = None, risk_data_dir: str = "risk_data"):
        self.limits = limits or RiskLimits()
        self.risk_data_dir = risk_data_dir
        self.logger = logging.getLogger(__name__)

        # 创建风险数据目录
        os.makedirs(self.risk_data_dir, exist_ok=True)

        # 风险状态跟踪
        self.daily_trades = 0
        self.current_drawdown = 0.0
        self.peak_value = 0.0
        self.current_value = 0.0

        # 持仓和风险数据
        self.positions = {}
        self.price_history = {}
        self.correlation_matrix = pd.DataFrame()
        self.sector_exposure = {}

        # 风险事件记录
        self.risk_events = []

        # 黑名单和白名单
        self.blacklist = set(self.limits.blacklist)
        self.whitelist = set(self.limits.whitelist) if self.limits.whitelist else None

        # 加载历史风险数据
        self._load_risk_data()

        self.logger.info("高级风险管理器初始化完成")

    def _load_risk_data(self):
        """加载历史风险数据"""
        try:
            # 加载价格历史数据
            price_file = f"{self.risk_data_dir}/price_history.json"
            if os.path.exists(price_file):
                with open(price_file, 'r') as f:
                    self.price_history = json.load(f)

            # 加载相关性矩阵
            corr_file = f"{self.risk_data_dir}/correlation_matrix.csv"
            if os.path.exists(corr_file):
                self.correlation_matrix = pd.read_csv(corr_file, index_col=0)

            # 加载行业敞口数据
            sector_file = f"{self.risk_data_dir}/sector_exposure.json"
            if os.path.exists(sector_file):
                with open(sector_file, 'r') as f:
                    self.sector_exposure = json.load(f)

        except Exception as e:
            self.logger.error(f"加载风险数据失败: {e}")

    def _save_risk_data(self):
        """保存风险数据"""
        try:
            # 保存价格历史
            with open(f"{self.risk_data_dir}/price_history.json", 'w') as f:
                json.dump(self.price_history, f)

            # 保存相关性矩阵
            if not self.correlation_matrix.empty:
                self.correlation_matrix.to_csv(f"{self.risk_data_dir}/correlation_matrix.csv")

            # 保存行业敞口
            with open(f"{self.risk_data_dir}/sector_exposure.json", 'w') as f:
                json.dump(self.sector_exposure, f)

        except Exception as e:
            self.logger.error(f"保存风险数据失败: {e}")

    def check_trade_allowed(self, symbol: str, size: float,
                          current_portfolio: Dict, price: float = None) -> Dict[str, Any]:
        """
        综合风险检查

        Args:
            symbol: 股票代码
            size: 交易数量
            current_portfolio: 当前投资组合
            price: 当前价格

        Returns:
            检查结果字典
        """
        try:
            result = {
                'allowed': True,
                'reasons': [],
                'warnings': [],
                'risk_score': 0.0
            }

            # 基础检查
            basic_checks = self._basic_risk_checks(symbol, size, current_portfolio)
            if not basic_checks['allowed']:
                result['allowed'] = False
                result['reasons'].extend(basic_checks['reasons'])

            # 高级风险检查
            advanced_checks = self._advanced_risk_checks(symbol, size, current_portfolio, price)
            if not advanced_checks['allowed']:
                result['allowed'] = False
                result['reasons'].extend(advanced_checks['reasons'])

            result['warnings'].extend(advanced_checks.get('warnings', []))
            result['risk_score'] = advanced_checks.get('risk_score', 0.0)

            # 记录风险事件
            if not result['allowed'] or result['warnings']:
                self._record_risk_event(symbol, size, result)

            return result

        except Exception as e:
            self.logger.error(f"风险检查失败: {e}")
            return {'allowed': False, 'reasons': [f'风险检查系统错误: {e}'], 'warnings': [], 'risk_score': 1.0}

    def _basic_risk_checks(self, symbol: str, size: float, current_portfolio: Dict) -> Dict[str, Any]:
        """基础风险检查"""
        result = {'allowed': True, 'reasons': []}

        # 黑名单检查
        if symbol in self.blacklist:
            result['allowed'] = False
            result['reasons'].append(f"股票 {symbol} 在黑名单中")

        # 白名单检查
        if self.whitelist and symbol not in self.whitelist:
            result['allowed'] = False
            result['reasons'].append(f"股票 {symbol} 不在白名单中")

        # 日交易次数检查
        if self.daily_trades >= self.limits.max_daily_trades:
            result['allowed'] = False
            result['reasons'].append(f"日交易次数超限: {self.daily_trades}/{self.limits.max_daily_trades}")

        # 回撤检查
        if self.current_drawdown > self.limits.max_drawdown:
            result['allowed'] = False
            result['reasons'].append(f"当前回撤超限: {self.current_drawdown:.2%}/{self.limits.max_drawdown:.2%}")

        # 单个持仓大小检查
        if current_portfolio:
            total_value = sum(abs(v) for v in current_portfolio.values())
            if total_value > 0:
                # 计算新交易后的持仓比例
                new_total_value = total_value + abs(size)
                position_pct = abs(size) / new_total_value
                if position_pct > self.limits.max_position_size:
                    result['allowed'] = False
                    result['reasons'].append(f"单个持仓超限: {position_pct:.2%}/{self.limits.max_position_size:.2%}")

        return result

    def _advanced_risk_checks(self, symbol: str, size: float,
                            current_portfolio: Dict, price: float = None) -> Dict[str, Any]:
        """高级风险检查"""
        result = {'allowed': True, 'reasons': [], 'warnings': [], 'risk_score': 0.0}

        try:
            # 相关性检查
            if not self.correlation_matrix.empty and symbol in self.correlation_matrix.columns:
                high_corr_symbols = []
                for other_symbol in current_portfolio.keys():
                    if (other_symbol in self.correlation_matrix.index and
                        abs(self.correlation_matrix.loc[symbol, other_symbol]) > self.limits.max_correlation):
                        high_corr_symbols.append(other_symbol)

                if high_corr_symbols:
                    result['warnings'].append(f"与持仓股票高度相关: {high_corr_symbols}")
                    result['risk_score'] += 0.2

            # 行业集中度检查
            if symbol in self.sector_exposure:
                sector = self.sector_exposure[symbol]
                sector_exposure = sum(v for k, v in current_portfolio.items()
                                    if k in self.sector_exposure and self.sector_exposure[k] == sector)
                total_value = sum(abs(v) for v in current_portfolio.values())

                if total_value > 0:
                    sector_pct = (sector_exposure + abs(size)) / total_value
                    if sector_pct > self.limits.max_sector_exposure:
                        result['allowed'] = False
                        result['reasons'].append(f"行业敞口超限: {sector_pct:.2%}/{self.limits.max_sector_exposure:.2%}")

            # 波动率检查
            if symbol in self.price_history and len(self.price_history[symbol]) > 20:
                prices = self.price_history[symbol][-20:]  # 最近20个价格
                returns = np.diff(prices) / prices[:-1]
                volatility = np.std(returns) * np.sqrt(252)  # 年化波动率

                if volatility > self.limits.volatility_threshold:
                    result['warnings'].append(f"高波动率股票: {volatility:.2%}")
                    result['risk_score'] += 0.3

            # 流动性检查（简化版本）
            if price and abs(size * price) > self.limits.liquidity_threshold:
                result['warnings'].append(f"大额交易可能影响流动性")
                result['risk_score'] += 0.1

            # 时间检查
            current_time = datetime.now().strftime('%H:%M')
            trading_start = self.limits.trading_hours['start']
            trading_end = self.limits.trading_hours['end']

            if not (trading_start <= current_time <= trading_end):
                result['warnings'].append(f"非交易时间: {current_time}")
                result['risk_score'] += 0.1

            return result

        except Exception as e:
            self.logger.error(f"高级风险检查失败: {e}")
            return {'allowed': True, 'reasons': [], 'warnings': [f'高级风险检查错误: {e}'], 'risk_score': 0.1}

    def _record_risk_event(self, symbol: str, size: float, result: Dict[str, Any]):
        """记录风险事件"""
        try:
            event = {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol,
                'size': size,
                'allowed': result['allowed'],
                'reasons': result['reasons'],
                'warnings': result['warnings'],
                'risk_score': result['risk_score']
            }

            self.risk_events.append(event)

            # 保存到文件
            events_file = f"{self.risk_data_dir}/risk_events.json"
            with open(events_file, 'w') as f:
                json.dump(self.risk_events[-1000:], f, indent=2)  # 只保留最近1000个事件

        except Exception as e:
            self.logger.error(f"记录风险事件失败: {e}")

    def update_portfolio(self, positions: Dict[str, float], prices: Dict[str, float] = None):
        """
        更新投资组合信息

        Args:
            positions: 持仓字典 {symbol: quantity}
            prices: 价格字典 {symbol: price}
        """
        try:
            self.positions = positions.copy()

            # 更新价格历史
            if prices:
                for symbol, price in prices.items():
                    if symbol not in self.price_history:
                        self.price_history[symbol] = []

                    self.price_history[symbol].append(price)
                    # 只保留最近100个价格
                    if len(self.price_history[symbol]) > 100:
                        self.price_history[symbol] = self.price_history[symbol][-100:]

            # 计算当前组合价值
            if prices:
                self.current_value = sum(positions.get(symbol, 0) * prices.get(symbol, 0)
                                       for symbol in set(positions.keys()) | set(prices.keys()))

                # 更新峰值和回撤
                if self.current_value > self.peak_value:
                    self.peak_value = self.current_value

                if self.peak_value > 0:
                    self.current_drawdown = (self.peak_value - self.current_value) / self.peak_value

            # 保存风险数据
            self._save_risk_data()

        except Exception as e:
            self.logger.error(f"更新投资组合失败: {e}")

    def calculate_var(self, confidence_level: float = 0.05, time_horizon: int = 1) -> float:
        """
        计算投资组合VaR (Value at Risk)

        Args:
            confidence_level: 置信水平
            time_horizon: 时间范围（天）

        Returns:
            VaR值
        """
        try:
            if not self.positions or not self.price_history:
                return 0.0

            # 计算各股票的收益率
            returns_data = {}
            for symbol in self.positions.keys():
                if symbol in self.price_history and len(self.price_history[symbol]) > 20:
                    prices = self.price_history[symbol][-20:]
                    returns = np.diff(prices) / prices[:-1]
                    returns_data[symbol] = returns

            if not returns_data:
                return 0.0

            # 构建收益率矩阵
            min_length = min(len(returns) for returns in returns_data.values())
            returns_matrix = np.array([returns[-min_length:] for returns in returns_data.values()]).T

            # 计算投资组合权重
            total_value = sum(abs(pos) for pos in self.positions.values())
            if total_value == 0:
                return 0.0

            weights = np.array([self.positions.get(symbol, 0) / total_value
                              for symbol in returns_data.keys()])

            # 计算投资组合收益率
            portfolio_returns = np.dot(returns_matrix, weights)

            # 计算VaR
            var = np.percentile(portfolio_returns, confidence_level * 100) * np.sqrt(time_horizon)

            return abs(var)

        except Exception as e:
            self.logger.error(f"计算VaR失败: {e}")
            return 0.0
    
    def generate_risk_report(self) -> Dict[str, Any]:
        """
        生成风险报告

        Returns:
            风险报告字典
        """
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_summary': {
                    'total_positions': len(self.positions),
                    'current_value': self.current_value,
                    'peak_value': self.peak_value,
                    'current_drawdown': self.current_drawdown,
                    'daily_trades': self.daily_trades
                },
                'risk_metrics': {
                    'var_5pct': self.calculate_var(0.05),
                    'var_1pct': self.calculate_var(0.01),
                    'max_drawdown_limit': self.limits.max_drawdown,
                    'max_position_size_limit': self.limits.max_position_size,
                    'max_total_exposure_limit': self.limits.max_total_exposure
                },
                'risk_events_today': len([e for e in self.risk_events
                                        if e['timestamp'].startswith(datetime.now().strftime('%Y-%m-%d'))]),
                'blacklist': list(self.blacklist),
                'whitelist': list(self.whitelist) if self.whitelist else None
            }

            # 保存报告
            report_file = f"{self.risk_data_dir}/risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            return report

        except Exception as e:
            self.logger.error(f"生成风险报告失败: {e}")
            return {}
    
    def update_drawdown(self, current_value: float, peak_value: float):
        """更新回撤"""
        self.current_drawdown = (peak_value - current_value) / peak_value
        
        if self.current_drawdown > self.limits.max_drawdown:
            self.logger.critical("Maximum drawdown exceeded!")
            return False
        return True
    
    def stress_test(self, scenarios: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        压力测试

        Args:
            scenarios: 压力测试场景列表 [{'AAPL': -0.2, 'MSFT': -0.15}, ...]

        Returns:
            压力测试结果
        """
        try:
            results = {}

            for i, scenario in enumerate(scenarios):
                scenario_name = f"Scenario_{i+1}"
                portfolio_loss = 0.0

                for symbol, shock in scenario.items():
                    if symbol in self.positions:
                        position_value = self.positions[symbol]
                        if symbol in self.price_history and self.price_history[symbol]:
                            current_price = self.price_history[symbol][-1]
                            loss = position_value * current_price * shock
                            portfolio_loss += loss

                loss_pct = portfolio_loss / self.current_value if self.current_value > 0 else 0

                results[scenario_name] = {
                    'scenario': scenario,
                    'portfolio_loss': portfolio_loss,
                    'loss_percentage': loss_pct,
                    'exceeds_limit': abs(loss_pct) > self.limits.stress_test_loss_limit
                }

            return results

        except Exception as e:
            self.logger.error(f"压力测试失败: {e}")
            return {}

    def add_to_blacklist(self, symbol: str):
        """添加到黑名单"""
        self.blacklist.add(symbol)
        self.logger.info(f"Added {symbol} to blacklist")

    def remove_from_blacklist(self, symbol: str):
        """从黑名单移除"""
        self.blacklist.discard(symbol)
        self.logger.info(f"Removed {symbol} from blacklist")

    def update_daily_trades(self, count: int):
        """更新日交易次数"""
        self.daily_trades = count

    def reset_daily_trades(self):
        """重置日交易次数"""
        self.daily_trades = 0
        self.logger.info("Daily trades count reset")