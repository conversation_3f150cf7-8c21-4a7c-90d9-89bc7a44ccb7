"""
高级回测引擎
支持多策略、多周期、多股票、多参数回测
"""

import backtrader as bt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
from datetime import datetime, timedelta
import logging
import os
from typing import Dict, List, Any, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools
from pathlib import Path
import json
import pickle

from ..data.data_manager import DataManager


class AdvancedBacktestEngine:
    """
    高级回测引擎
    
    功能特性:
    1. 支持单策略和多策略回测
    2. 支持多时间周期回测
    3. 支持多股票回测
    4. 支持多参数优化回测
    5. 并行处理提升性能
    6. 详细的回测报告和可视化
    """
    
    def __init__(self, data_manager: DataManager, initial_cash: float = 100000, 
                 commission: float = 0.001, results_dir: str = "backtest_results"):
        """
        初始化高级回测引擎
        
        Args:
            data_manager: 数据管理器
            initial_cash: 初始资金
            commission: 手续费率
            results_dir: 结果保存目录
        """
        self.data_manager = data_manager
        self.initial_cash = initial_cash
        self.commission = commission
        self.results_dir = results_dir
        self.logger = logging.getLogger(__name__)
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(f"{self.results_dir}/plots", exist_ok=True)
        os.makedirs(f"{self.results_dir}/reports", exist_ok=True)
        
        self.logger.info("高级回测引擎初始化完成")
    
    def single_strategy_backtest(self, strategy_class, symbols: List[str], 
                                timeframe: str = '1d', start_date: str = None, 
                                end_date: str = None, strategy_params: Dict = None) -> Dict:
        """
        单策略回测
        
        Args:
            strategy_class: 策略类
            symbols: 股票代码列表
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            
        Returns:
            回测结果字典
        """
        try:
            self.logger.info(f"开始单策略回测: {strategy_class.__name__}")
            
            # 创建Cerebro引擎
            cerebro = bt.Cerebro()
            
            # 添加策略
            if strategy_params:
                cerebro.addstrategy(strategy_class, **strategy_params)
            else:
                cerebro.addstrategy(strategy_class)
            
            # 添加数据
            for symbol in symbols:
                data = self._prepare_data(symbol, timeframe, start_date, end_date)
                if not data.empty:
                    bt_data = self._convert_to_bt_data(data, symbol)
                    cerebro.adddata(bt_data, name=symbol)
            
            # 设置初始资金和手续费
            cerebro.broker.setcash(self.initial_cash)
            cerebro.broker.setcommission(commission=self.commission)
            
            # 添加分析器
            self._add_analyzers(cerebro)
            
            # 运行回测
            initial_value = cerebro.broker.getvalue()
            results = cerebro.run()
            final_value = cerebro.broker.getvalue()
            
            # 处理结果
            backtest_result = self._process_results(
                results[0], initial_value, final_value, 
                strategy_class.__name__, symbols, timeframe
            )
            
            # 保存结果
            self._save_backtest_result(backtest_result, 'single_strategy')
            
            self.logger.info(f"单策略回测完成: {strategy_class.__name__}")
            return backtest_result
            
        except Exception as e:
            self.logger.error(f"单策略回测失败: {e}")
            return {}
    
    def multi_strategy_backtest(self, strategy_configs: List[Dict], 
                               symbols: List[str], timeframe: str = '1d',
                               start_date: str = None, end_date: str = None) -> Dict:
        """
        多策略回测
        
        Args:
            strategy_configs: 策略配置列表 [{'class': StrategyClass, 'params': {...}}, ...]
            symbols: 股票代码列表
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            多策略回测结果
        """
        try:
            self.logger.info(f"开始多策略回测，共 {len(strategy_configs)} 个策略")
            
            results = {}
            
            for i, config in enumerate(strategy_configs):
                strategy_class = config['class']
                strategy_params = config.get('params', {})
                strategy_name = f"{strategy_class.__name__}_{i}"
                
                self.logger.info(f"回测策略 {i+1}/{len(strategy_configs)}: {strategy_name}")
                
                # 单独回测每个策略
                result = self.single_strategy_backtest(
                    strategy_class, symbols, timeframe, 
                    start_date, end_date, strategy_params
                )
                
                results[strategy_name] = result
            
            # 生成比较报告
            comparison_report = self._generate_strategy_comparison(results)
            
            # 保存比较结果
            self._save_backtest_result(comparison_report, 'multi_strategy_comparison')
            
            self.logger.info("多策略回测完成")
            return {
                'individual_results': results,
                'comparison': comparison_report
            }
            
        except Exception as e:
            self.logger.error(f"多策略回测失败: {e}")
            return {}
    
    def multi_timeframe_backtest(self, strategy_class, symbols: List[str],
                                timeframes: List[str], start_date: str = None,
                                end_date: str = None, strategy_params: Dict = None) -> Dict:
        """
        多时间周期回测
        
        Args:
            strategy_class: 策略类
            symbols: 股票代码列表
            timeframes: 时间周期列表
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            
        Returns:
            多时间周期回测结果
        """
        try:
            self.logger.info(f"开始多时间周期回测，共 {len(timeframes)} 个周期")
            
            results = {}
            
            for timeframe in timeframes:
                self.logger.info(f"回测时间周期: {timeframe}")
                
                result = self.single_strategy_backtest(
                    strategy_class, symbols, timeframe,
                    start_date, end_date, strategy_params
                )
                
                results[timeframe] = result
            
            # 生成时间周期比较报告
            comparison_report = self._generate_timeframe_comparison(results)
            
            # 保存比较结果
            self._save_backtest_result(comparison_report, 'multi_timeframe_comparison')
            
            self.logger.info("多时间周期回测完成")
            return {
                'individual_results': results,
                'comparison': comparison_report
            }
            
        except Exception as e:
            self.logger.error(f"多时间周期回测失败: {e}")
            return {}
    
    def multi_symbol_backtest(self, strategy_class, symbol_groups: List[List[str]],
                             timeframe: str = '1d', start_date: str = None,
                             end_date: str = None, strategy_params: Dict = None) -> Dict:
        """
        多股票组合回测
        
        Args:
            strategy_class: 策略类
            symbol_groups: 股票组合列表 [['AAPL', 'MSFT'], ['GOOGL', 'AMZN'], ...]
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            
        Returns:
            多股票组合回测结果
        """
        try:
            self.logger.info(f"开始多股票组合回测，共 {len(symbol_groups)} 个组合")
            
            results = {}
            
            for i, symbols in enumerate(symbol_groups):
                group_name = f"Group_{i+1}_{'_'.join(symbols[:3])}"  # 限制名称长度
                self.logger.info(f"回测股票组合 {i+1}/{len(symbol_groups)}: {symbols}")
                
                result = self.single_strategy_backtest(
                    strategy_class, symbols, timeframe,
                    start_date, end_date, strategy_params
                )
                
                results[group_name] = result
            
            # 生成股票组合比较报告
            comparison_report = self._generate_symbol_comparison(results)
            
            # 保存比较结果
            self._save_backtest_result(comparison_report, 'multi_symbol_comparison')
            
            self.logger.info("多股票组合回测完成")
            return {
                'individual_results': results,
                'comparison': comparison_report
            }
            
        except Exception as e:
            self.logger.error(f"多股票组合回测失败: {e}")
            return {}

    def parameter_optimization_backtest(self, strategy_class, symbols: List[str],
                                       param_ranges: Dict[str, List], timeframe: str = '1d',
                                       start_date: str = None, end_date: str = None,
                                       max_workers: int = 4, optimization_target: str = 'total_return') -> Dict:
        """
        参数优化回测

        Args:
            strategy_class: 策略类
            symbols: 股票代码列表
            param_ranges: 参数范围字典 {'param1': [val1, val2, ...], 'param2': [...]}
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            max_workers: 最大并发数
            optimization_target: 优化目标 ('total_return', 'sharpe_ratio', 'max_drawdown')

        Returns:
            参数优化结果
        """
        try:
            self.logger.info(f"开始参数优化回测，优化目标: {optimization_target}")

            # 生成所有参数组合
            param_names = list(param_ranges.keys())
            param_values = list(param_ranges.values())
            param_combinations = list(itertools.product(*param_values))

            self.logger.info(f"总共 {len(param_combinations)} 个参数组合")

            results = []

            # 并行执行回测
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_params = {}

                for i, param_combo in enumerate(param_combinations):
                    params = dict(zip(param_names, param_combo))

                    future = executor.submit(
                        self.single_strategy_backtest,
                        strategy_class, symbols, timeframe,
                        start_date, end_date, params
                    )
                    future_to_params[future] = (i, params)

                # 收集结果
                for future in as_completed(future_to_params):
                    i, params = future_to_params[future]
                    try:
                        result = future.result()
                        if result:
                            result['params'] = params
                            result['param_index'] = i
                            results.append(result)

                        # 显示进度
                        progress = len(results) / len(param_combinations) * 100
                        self.logger.info(f"优化进度: {progress:.1f}% ({len(results)}/{len(param_combinations)})")

                    except Exception as e:
                        self.logger.error(f"参数组合 {i} 回测失败: {e}")

            # 按优化目标排序
            if results:
                if optimization_target == 'total_return':
                    results.sort(key=lambda x: x.get('total_return', -float('inf')), reverse=True)
                elif optimization_target == 'sharpe_ratio':
                    results.sort(key=lambda x: x.get('sharpe_ratio', -float('inf')), reverse=True)
                elif optimization_target == 'max_drawdown':
                    results.sort(key=lambda x: abs(x.get('max_drawdown', float('inf'))))

                # 生成优化报告
                optimization_report = self._generate_optimization_report(results, optimization_target)

                # 保存优化结果
                self._save_backtest_result(optimization_report, 'parameter_optimization')

                self.logger.info(f"参数优化完成，最佳参数: {results[0]['params']}")

                return {
                    'best_result': results[0],
                    'all_results': results,
                    'optimization_report': optimization_report
                }
            else:
                self.logger.warning("参数优化未产生有效结果")
                return {}

        except Exception as e:
            self.logger.error(f"参数优化回测失败: {e}")
            return {}

    def _prepare_data(self, symbol: str, timeframe: str, start_date: str = None,
                     end_date: str = None) -> pd.DataFrame:
        """
        准备回测数据

        Args:
            symbol: 股票代码
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            准备好的数据DataFrame
        """
        try:
            # 从数据管理器加载数据
            data = self.data_manager.load_stock_data(symbol, timeframe, start_date, end_date)

            if data.empty:
                self.logger.warning(f"未找到 {symbol} {timeframe} 的数据")
                return pd.DataFrame()

            # 确保数据格式正确
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
            for col in required_columns:
                if col not in data.columns:
                    self.logger.error(f"数据缺少必要列: {col}")
                    return pd.DataFrame()

            # 转换日期列
            data['Date'] = pd.to_datetime(data['Date'])
            data = data.sort_values('Date').reset_index(drop=True)

            return data

        except Exception as e:
            self.logger.error(f"准备数据失败 {symbol} {timeframe}: {e}")
            return pd.DataFrame()

    def _convert_to_bt_data(self, data: pd.DataFrame, symbol: str) -> bt.feeds.PandasData:
        """
        转换为Backtrader数据格式

        Args:
            data: 数据DataFrame
            symbol: 股票代码

        Returns:
            Backtrader数据对象
        """
        try:
            # 设置日期为索引
            data_copy = data.copy()
            data_copy.set_index('Date', inplace=True)

            # 创建Backtrader数据对象
            bt_data = bt.feeds.PandasData(
                dataname=data_copy,
                datetime=None,
                open='Open',
                high='High',
                low='Low',
                close='Close',
                volume='Volume',
                openinterest=None
            )

            return bt_data

        except Exception as e:
            self.logger.error(f"转换Backtrader数据格式失败: {e}")
            raise

    def _add_analyzers(self, cerebro):
        """
        添加分析器

        Args:
            cerebro: Backtrader引擎
        """
        try:
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
            cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')  # System Quality Number
            cerebro.addanalyzer(bt.analyzers.VWR, _name='vwr')  # Variability-Weighted Return

        except Exception as e:
            self.logger.error(f"添加分析器失败: {e}")

    def _process_results(self, strategy_result, initial_value: float, final_value: float,
                        strategy_name: str, symbols: List[str], timeframe: str) -> Dict:
        """
        处理回测结果

        Args:
            strategy_result: 策略结果
            initial_value: 初始资金
            final_value: 最终资金
            strategy_name: 策略名称
            symbols: 股票代码列表
            timeframe: 时间周期

        Returns:
            处理后的结果字典
        """
        try:
            # 计算基本指标
            total_return = (final_value - initial_value) / initial_value * 100

            # 获取分析器结果
            analyzers = strategy_result.analyzers

            # Sharpe比率
            sharpe_ratio = analyzers.sharpe.get_analysis().get('sharperatio', 0)
            if sharpe_ratio is None:
                sharpe_ratio = 0

            # 回撤分析
            drawdown_analysis = analyzers.drawdown.get_analysis()
            max_drawdown = drawdown_analysis.get('max', {}).get('drawdown', 0)

            # 交易分析
            trade_analysis = analyzers.trades.get_analysis()
            total_trades = trade_analysis.get('total', {}).get('total', 0)
            winning_trades = trade_analysis.get('won', {}).get('total', 0)
            losing_trades = trade_analysis.get('lost', {}).get('total', 0)

            # 胜率
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # 平均盈利和亏损
            avg_win = trade_analysis.get('won', {}).get('pnl', {}).get('average', 0)
            avg_loss = trade_analysis.get('lost', {}).get('pnl', {}).get('average', 0)

            # 盈亏比
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # SQN (System Quality Number)
            sqn = analyzers.sqn.get_analysis().get('sqn', 0)

            # VWR (Variability-Weighted Return)
            vwr = analyzers.vwr.get_analysis().get('vwr', 0)

            result = {
                'strategy_name': strategy_name,
                'symbols': symbols,
                'timeframe': timeframe,
                'initial_value': initial_value,
                'final_value': final_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_loss_ratio': profit_loss_ratio,
                'sqn': sqn,
                'vwr': vwr,
                'backtest_date': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            self.logger.error(f"处理回测结果失败: {e}")
            return {}

    def _generate_strategy_comparison(self, results: Dict) -> Dict:
        """
        生成策略比较报告

        Args:
            results: 策略结果字典

        Returns:
            比较报告
        """
        try:
            if not results:
                return {}

            comparison = {
                'summary': {},
                'rankings': {},
                'statistics': {}
            }

            # 提取关键指标
            metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'total_trades']

            for metric in metrics:
                values = [(name, result.get(metric, 0)) for name, result in results.items() if result]
                if values:
                    # 按指标排序
                    if metric == 'max_drawdown':
                        values.sort(key=lambda x: abs(x[1]))  # 回撤越小越好
                    else:
                        values.sort(key=lambda x: x[1], reverse=True)  # 其他指标越大越好

                    comparison['rankings'][metric] = values
                    comparison['statistics'][metric] = {
                        'best': values[0],
                        'worst': values[-1],
                        'average': np.mean([v[1] for v in values]),
                        'std': np.std([v[1] for v in values])
                    }

            # 综合评分（简单加权）
            weights = {
                'total_return': 0.3,
                'sharpe_ratio': 0.3,
                'max_drawdown': 0.2,  # 负权重，回撤越小越好
                'win_rate': 0.2
            }

            scores = {}
            for name, result in results.items():
                if result:
                    score = 0
                    for metric, weight in weights.items():
                        value = result.get(metric, 0)
                        if metric == 'max_drawdown':
                            score -= weight * abs(value)  # 回撤是负面指标
                        else:
                            score += weight * value
                    scores[name] = score

            # 按综合评分排序
            sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            comparison['overall_ranking'] = sorted_scores

            return comparison

        except Exception as e:
            self.logger.error(f"生成策略比较报告失败: {e}")
            return {}

    def _generate_timeframe_comparison(self, results: Dict) -> Dict:
        """
        生成时间周期比较报告

        Args:
            results: 时间周期结果字典

        Returns:
            比较报告
        """
        try:
            return self._generate_strategy_comparison(results)  # 复用策略比较逻辑
        except Exception as e:
            self.logger.error(f"生成时间周期比较报告失败: {e}")
            return {}

    def _generate_symbol_comparison(self, results: Dict) -> Dict:
        """
        生成股票组合比较报告

        Args:
            results: 股票组合结果字典

        Returns:
            比较报告
        """
        try:
            return self._generate_strategy_comparison(results)  # 复用策略比较逻辑
        except Exception as e:
            self.logger.error(f"生成股票组合比较报告失败: {e}")
            return {}

    def _generate_optimization_report(self, results: List[Dict], target: str) -> Dict:
        """
        生成参数优化报告

        Args:
            results: 优化结果列表
            target: 优化目标

        Returns:
            优化报告
        """
        try:
            if not results:
                return {}

            report = {
                'optimization_target': target,
                'total_combinations': len(results),
                'best_result': results[0],
                'worst_result': results[-1],
                'top_10': results[:10],
                'parameter_analysis': {}
            }

            # 分析参数影响
            if 'params' in results[0]:
                param_names = list(results[0]['params'].keys())

                for param_name in param_names:
                    param_values = {}
                    for result in results:
                        param_val = result['params'][param_name]
                        target_val = result.get(target, 0)

                        if param_val not in param_values:
                            param_values[param_val] = []
                        param_values[param_val].append(target_val)

                    # 计算每个参数值的平均表现
                    param_performance = {}
                    for val, performances in param_values.items():
                        param_performance[val] = {
                            'average': np.mean(performances),
                            'std': np.std(performances),
                            'count': len(performances)
                        }

                    report['parameter_analysis'][param_name] = param_performance

            return report

        except Exception as e:
            self.logger.error(f"生成参数优化报告失败: {e}")
            return {}

    def _save_backtest_result(self, result: Dict, result_type: str):
        """
        保存回测结果

        Args:
            result: 回测结果
            result_type: 结果类型
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.results_dir}/reports/{result_type}_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"回测结果已保存: {filename}")

        except Exception as e:
            self.logger.error(f"保存回测结果失败: {e}")
