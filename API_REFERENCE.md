# 量化交易框架 API 参考文档

## 目录
1. [数据管理 API](#1-数据管理-api)
2. [回测引擎 API](#2-回测引擎-api)
3. [交易模块 API](#3-交易模块-api)
4. [风险管理 API](#4-风险管理-api)
5. [可视化 API](#5-可视化-api)
6. [工具函数 API](#6-工具函数-api)

---

## 1. 数据管理 API

### DataManager 类

#### 初始化
```python
from quant_trading_system.data.data_manager import DataManager

# 创建数据管理器实例
data_manager = DataManager(
    data_dir="data",           # 数据存储目录
    use_csv=True,             # 使用CSV存储
    cache_size=1000           # 缓存大小
)
```

#### 核心方法

##### download_stock_data()
下载股票数据
```python
def download_stock_data(self, symbol: str, timeframe: str = '1d', 
                       period: str = '1y', force_update: bool = False) -> pd.DataFrame
```

**参数:**
- `symbol` (str): 股票代码，如 'AAPL'
- `timeframe` (str): 时间周期，支持 '1m', '5m', '15m', '30m', '1h', '1d', '1wk', '1mo'
- `period` (str): 数据周期，如 '1y', '6mo', '3mo', '1mo'
- `force_update` (bool): 是否强制更新

**返回:**
- `pd.DataFrame`: 股票数据，包含 Date, Open, High, Low, Close, Volume 列

**示例:**
```python
# 下载苹果公司1年的日线数据
data = data_manager.download_stock_data('AAPL', '1d', '1y')
print(data.head())
```

##### load_stock_data()
加载本地股票数据
```python
def load_stock_data(self, symbol: str, timeframe: str = '1d', 
                   start_date: str = None, end_date: str = None) -> pd.DataFrame
```

**参数:**
- `symbol` (str): 股票代码
- `timeframe` (str): 时间周期
- `start_date` (str): 开始日期，格式 'YYYY-MM-DD'
- `end_date` (str): 结束日期，格式 'YYYY-MM-DD'

**返回:**
- `pd.DataFrame`: 股票数据

**示例:**
```python
# 加载指定日期范围的数据
data = data_manager.load_stock_data('AAPL', '1d', '2023-01-01', '2023-12-31')
```

##### update_all_data()
批量更新所有数据
```python
def update_all_data(self, symbols: List[str] = None, 
                   timeframes: List[str] = None, max_workers: int = 4) -> Dict[str, bool]
```

**参数:**
- `symbols` (List[str]): 股票代码列表，None表示更新所有
- `timeframes` (List[str]): 时间周期列表，None表示更新所有
- `max_workers` (int): 最大并发数

**返回:**
- `Dict[str, bool]`: 更新结果，键为股票代码，值为是否成功

##### get_data_summary()
获取数据概览
```python
def get_data_summary(self) -> Dict[str, Any]
```

**返回:**
- `Dict[str, Any]`: 数据概览信息

---

## 2. 回测引擎 API

### AdvancedBacktestEngine 类

#### 初始化
```python
from quant_trading_system.backtest.advanced_backtest_engine import AdvancedBacktestEngine

# 创建回测引擎
engine = AdvancedBacktestEngine(
    data_manager=data_manager,    # 数据管理器实例
    initial_capital=100000,       # 初始资金
    commission=0.001,             # 手续费率
    slippage=0.0005              # 滑点
)
```

#### 核心方法

##### single_strategy_backtest()
单策略回测
```python
def single_strategy_backtest(self, strategy_class, symbols: List[str], 
                           timeframe: str = '1d', strategy_params: Dict = None,
                           start_date: str = None, end_date: str = None) -> Dict[str, Any]
```

**参数:**
- `strategy_class`: 策略类
- `symbols` (List[str]): 股票代码列表
- `timeframe` (str): 时间周期
- `strategy_params` (Dict): 策略参数
- `start_date` (str): 开始日期
- `end_date` (str): 结束日期

**返回:**
- `Dict[str, Any]`: 回测结果

**示例:**
```python
from quant_trading_system.strategy.ma5_strategy import MA5Strategy

result = engine.single_strategy_backtest(
    strategy_class=MA5Strategy,
    symbols=['AAPL', 'MSFT'],
    timeframe='1d',
    strategy_params={'ma_period': 5}
)

print(f"总收益率: {result['total_return']:.2f}%")
print(f"夏普比率: {result['sharpe_ratio']:.2f}")
```

##### multi_strategy_backtest()
多策略对比回测
```python
def multi_strategy_backtest(self, strategies: List[Dict], symbols: List[str],
                          timeframe: str = '1d', start_date: str = None,
                          end_date: str = None) -> Dict[str, Any]
```

**参数:**
- `strategies` (List[Dict]): 策略配置列表
- `symbols` (List[str]): 股票代码列表
- `timeframe` (str): 时间周期
- `start_date` (str): 开始日期
- `end_date` (str): 结束日期

**示例:**
```python
strategies = [
    {
        'name': 'MA5',
        'class': MA5Strategy,
        'params': {'ma_period': 5}
    },
    {
        'name': 'MA10',
        'class': MA5Strategy,
        'params': {'ma_period': 10}
    }
]

result = engine.multi_strategy_backtest(strategies, ['AAPL'])
```

##### parameter_optimization_backtest()
参数优化回测
```python
def parameter_optimization_backtest(self, strategy_class, symbols: List[str],
                                  param_ranges: Dict[str, List], 
                                  optimization_target: str = 'total_return',
                                  max_workers: int = 4) -> Dict[str, Any]
```

**参数:**
- `strategy_class`: 策略类
- `symbols` (List[str]): 股票代码列表
- `param_ranges` (Dict[str, List]): 参数范围
- `optimization_target` (str): 优化目标
- `max_workers` (int): 最大并发数

**示例:**
```python
param_ranges = {
    'ma_period': [5, 10, 15, 20],
    'threshold': [0.01, 0.02, 0.03]
}

result = engine.parameter_optimization_backtest(
    strategy_class=MA5Strategy,
    symbols=['AAPL'],
    param_ranges=param_ranges,
    optimization_target='sharpe_ratio'
)

print(f"最佳参数: {result['best_params']}")
```

---

## 3. 交易模块 API

### TradeRecorder 类

#### 初始化
```python
from quant_trading_system.trading.trade_recorder import TradeRecorder

# 创建交易记录器
recorder = TradeRecorder(
    data_dir="trading_data"    # 数据存储目录
)
```

#### 核心方法

##### record_order()
记录订单
```python
def record_order(self, order_data: Dict[str, Any]) -> str
```

**参数:**
- `order_data` (Dict): 订单数据

**返回:**
- `str`: 订单ID

**示例:**
```python
order_data = {
    'symbol': 'AAPL',
    'strategy': 'MA5Strategy',
    'action': 'BUY',
    'quantity': 100,
    'order_type': 'MKT',
    'price': 150.0,
    'notes': '买入信号'
}

order_id = recorder.record_order(order_data)
```

##### record_trade()
记录交易
```python
def record_trade(self, trade_data: Dict[str, Any]) -> str
```

**参数:**
- `trade_data` (Dict): 交易数据

**返回:**
- `str`: 交易ID

**示例:**
```python
from datetime import datetime, timedelta

trade_data = {
    'symbol': 'AAPL',
    'strategy': 'MA5Strategy',
    'action': 'BUY',
    'quantity': 100,
    'entry_price': 150.0,
    'exit_price': 155.0,
    'entry_time': datetime.now() - timedelta(hours=2),
    'exit_time': datetime.now(),
    'commission': 2.0,
    'trade_type': 'LONG'
}

trade_id = recorder.record_trade(trade_data)
```

### TradeAnalyzer 类

#### 初始化
```python
from quant_trading_system.trading.trade_analyzer import TradeAnalyzer

# 创建交易分析器
analyzer = TradeAnalyzer(
    trade_recorder=recorder,           # 交易记录器实例
    reports_dir="analysis_reports"     # 报告输出目录
)
```

#### 核心方法

##### analyze_strategy_performance()
分析策略表现
```python
def analyze_strategy_performance(self, strategy: str = None, symbol: str = None,
                               start_date: str = None, end_date: str = None) -> Dict[str, Any]
```

**参数:**
- `strategy` (str): 策略名称
- `symbol` (str): 股票代码
- `start_date` (str): 开始日期
- `end_date` (str): 结束日期

**返回:**
- `Dict[str, Any]`: 分析结果

**示例:**
```python
analysis = analyzer.analyze_strategy_performance(strategy='MA5Strategy')

metrics = analysis['basic_metrics']
print(f"总交易数: {metrics['total_trades']}")
print(f"胜率: {metrics['win_rate']:.1f}%")
print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
```

##### generate_performance_report()
生成交易报告
```python
def generate_performance_report(self, strategy: str = None, symbol: str = None,
                              save_charts: bool = True) -> str
```

**参数:**
- `strategy` (str): 策略名称
- `symbol` (str): 股票代码  
- `save_charts` (bool): 是否保存图表

**返回:**
- `str`: 报告文件路径

**示例:**
```python
report_path = analyzer.generate_performance_report(
    strategy='MA5Strategy',
    save_charts=True
)
print(f"报告已生成: {report_path}")
```
