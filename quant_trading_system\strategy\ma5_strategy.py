"""
5日均线策略
- 当价格上穿5日均线时买入
- 当价格下穿5日均线时卖出
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any
import logging


class MA5Strategy(bt.Strategy):
    """5日均线策略"""
    
    params = (
        ('ma_period', 5),  # 均线周期
        ('position_size', 0.95),  # 仓位大小（95%资金）
        ('debug', True),  # 调试模式
    )
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 计算5日均线
        self.ma5 = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.ma_period
        )
        
        # 交叉信号
        self.crossover = bt.indicators.CrossOver(self.data.close, self.ma5)
        
        # 记录交易信号
        self.order = None
        self.buy_price = None
        self.buy_comm = None
        
        if self.params.debug:
            self.logger.info(f"MA5Strategy initialized with period={self.params.ma_period}")
    
    def next(self):
        """策略主逻辑"""
        current_date = self.data.datetime.date(0)
        current_price = self.data.close[0]
        current_ma5 = self.ma5[0]
        
        # 如果有未完成的订单，跳过
        if self.order:
            return
        
        # 检查是否有足够的数据
        if len(self.data) < self.params.ma_period:
            return
        
        # 买入信号：价格上穿5日均线
        if not self.position and self.crossover[0] > 0:
            # 计算买入数量
            size = int(self.broker.getcash() * self.params.position_size / current_price)
            if size > 0:
                self.order = self.buy(size=size)
                if self.params.debug:
                    self.logger.info(f"{current_date}: BUY signal - Price: {current_price:.2f}, MA5: {current_ma5:.2f}, Size: {size}")
        
        # 卖出信号：价格下穿5日均线
        elif self.position and self.crossover[0] < 0:
            self.order = self.sell(size=self.position.size)
            if self.params.debug:
                self.logger.info(f"{current_date}: SELL signal - Price: {current_price:.2f}, MA5: {current_ma5:.2f}, Size: {self.position.size}")
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                self.buy_price = order.executed.price
                self.buy_comm = order.executed.comm
                if self.params.debug:
                    self.logger.info(f"BUY EXECUTED: Price: {order.executed.price:.2f}, "
                                   f"Size: {order.executed.size}, Comm: {order.executed.comm:.2f}")
            else:
                if self.params.debug:
                    profit = (order.executed.price - self.buy_price) * order.executed.size - order.executed.comm - self.buy_comm
                    self.logger.info(f"SELL EXECUTED: Price: {order.executed.price:.2f}, "
                                   f"Size: {order.executed.size}, Comm: {order.executed.comm:.2f}, "
                                   f"Profit: {profit:.2f}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            if self.params.debug:
                self.logger.warning(f"Order {order.status}")
        
        self.order = None
    
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
        
        if self.params.debug:
            self.logger.info(f"TRADE CLOSED: Profit: {trade.pnl:.2f}, Net Profit: {trade.pnlcomm:.2f}")


class MA5LiveStrategy:
    """5日均线实盘策略"""
    
    def __init__(self, ma_period=5, position_size=0.95):
        self.ma_period = ma_period
        self.position_size = position_size
        self.logger = logging.getLogger(__name__)
        self.price_history = []
        self.last_signal = None
        
    def add_price(self, price: float):
        """添加新的价格数据"""
        self.price_history.append(price)
        # 只保留需要的历史数据
        if len(self.price_history) > self.ma_period * 2:
            self.price_history = self.price_history[-self.ma_period * 2:]
    
    def calculate_ma5(self) -> float:
        """计算5日均线"""
        if len(self.price_history) < self.ma_period:
            return None
        return np.mean(self.price_history[-self.ma_period:])
    
    def get_signal(self, current_price: float) -> Dict[str, Any]:
        """获取交易信号"""
        self.add_price(current_price)
        
        if len(self.price_history) < self.ma_period + 1:
            return {'action': 'HOLD', 'reason': 'Insufficient data'}
        
        current_ma5 = self.calculate_ma5()
        prev_ma5 = np.mean(self.price_history[-self.ma_period-1:-1])
        prev_price = self.price_history[-2]
        
        # 检查交叉信号
        # 上穿：前一个价格在均线下方，当前价格在均线上方
        if prev_price <= prev_ma5 and current_price > current_ma5:
            signal = {
                'action': 'BUY',
                'symbol': 'AAPL',
                'quantity': None,  # 将在实际交易时计算
                'price': current_price,
                'ma5': current_ma5,
                'reason': f'Price crossed above MA5: {current_price:.2f} > {current_ma5:.2f}'
            }
            self.last_signal = 'BUY'
            return signal
        
        # 下穿：前一个价格在均线上方，当前价格在均线下方
        elif prev_price >= prev_ma5 and current_price < current_ma5:
            signal = {
                'action': 'SELL',
                'symbol': 'AAPL',
                'quantity': None,  # 将在实际交易时计算
                'price': current_price,
                'ma5': current_ma5,
                'reason': f'Price crossed below MA5: {current_price:.2f} < {current_ma5:.2f}'
            }
            self.last_signal = 'SELL'
            return signal
        
        return {
            'action': 'HOLD',
            'price': current_price,
            'ma5': current_ma5,
            'reason': f'No crossover signal: Price={current_price:.2f}, MA5={current_ma5:.2f}'
        }
