from tigeropen.tiger_open_client import TigerOpenClient
from tigeropen.trade.trade_client import TradeClient
from tigeropen.common.consts import OrderType, TimeInForce
import threading
import time
from typing import Dict, Callable
import logging

class LiveTrader:
    def __init__(self, tiger_client: TigerOpenClient, 
                 risk_manager, strategy_callback: Callable):
        self.client = tiger_client
        self.trade_client = TradeClient(tiger_client)
        self.risk_manager = risk_manager
        self.strategy_callback = strategy_callback
        self.is_running = False
        self.positions = {}
        self.logger = logging.getLogger(__name__)
    
    def start_trading(self):
        """启动实盘交易"""
        self.is_running = True
        
        # 启动行情订阅线程
        market_thread = threading.Thread(target=self._market_data_loop)
        market_thread.daemon = True
        market_thread.start()
        
        # 启动交易执行线程
        trading_thread = threading.Thread(target=self._trading_loop)
        trading_thread.daemon = True
        trading_thread.start()
        
        self.logger.info("Live trading started")
    
    def stop_trading(self):
        """停止交易"""
        self.is_running = False
        self.logger.info("Live trading stopped")
    
    def _market_data_loop(self):
        """行情数据循环"""
        while self.is_running:
            try:
                # 获取实时行情
                market_data = self._get_market_data()
                
                # 调用策略回调
                signals = self.strategy_callback(market_data)
                
                # 处理交易信号
                self._process_signals(signals)
                
                time.sleep(1)  # 1秒间隔
                
            except Exception as e:
                self.logger.error(f"Market data loop error: {e}")
    
    def _trading_loop(self):
        """交易执行循环"""
        while self.is_running:
            try:
                # 检查订单状态
                self._check_order_status()
                
                # 更新持仓
                self._update_positions()
                
                time.sleep(5)  # 5秒间隔
                
            except Exception as e:
                self.logger.error(f"Trading loop error: {e}")
    
    def place_order(self, symbol: str, quantity: int, 
                   order_type: str = "MKT") -> str:
        """下单"""
        if not self.risk_manager.check_trade_allowed(
            symbol, quantity, self.positions):
            return None
        
        try:
            order_id = self.trade_client.place_order(
                symbol=symbol,
                quantity=quantity,
                order_type=OrderType.MKT if order_type == "MKT" else OrderType.LMT,
                time_in_force=TimeInForce.DAY
            )
            
            self.logger.info(f"Order placed: {symbol}, {quantity}, {order_id}")
            return order_id
            
        except Exception as e:
            self.logger.error(f"Order placement failed: {e}")
            return None