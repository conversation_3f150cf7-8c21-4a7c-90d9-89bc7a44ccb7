from tigeropen.tiger_open_client import TigerOpenClient
from tigeropen.trade.trade_client import TradeClient
from tigeropen.common.consts import OrderType, SecurityType, Market
from tigeropen.common.util.contract_utils import stock_contract
from tigeropen.common.util.order_utils import limit_order, market_order
import threading
import time
from typing import Dict, Callable, List
import logging
from datetime import datetime

from .trade_recorder import TradeRecorder

class LiveTrader:
    def __init__(self, tiger_client: TigerOpenClient,
                 risk_manager, strategy_callback: Callable,
                 trade_recorder: TradeRecorder = None):
        self.client = tiger_client
        self.trade_client = None
        self.risk_manager = risk_manager
        self.strategy_callback = strategy_callback
        self.trade_recorder = trade_recorder or TradeRecorder()
        self.is_running = False
        self.positions = {}
        self.logger = logging.getLogger(__name__)
        self.account = None

        # 当前订单跟踪
        self.active_orders = {}
        self.open_positions = {}

        # 安全地初始化TradeClient
        if tiger_client:
            try:
                self.trade_client = TradeClient(tiger_client)
                self.account = getattr(tiger_client, 'account', None)
                self.logger.info("TradeClient初始化成功")
            except Exception as e:
                self.logger.warning(f"TradeClient初始化失败: {e}")
                self.trade_client = None
    
    def start_trading(self):
        """启动实盘交易"""
        self.is_running = True
        
        # 启动行情订阅线程
        market_thread = threading.Thread(target=self._market_data_loop)
        market_thread.daemon = True
        market_thread.start()
        
        # 启动交易执行线程
        trading_thread = threading.Thread(target=self._trading_loop)
        trading_thread.daemon = True
        trading_thread.start()
        
        self.logger.info("Live trading started")
    
    def stop_trading(self):
        """停止交易"""
        self.is_running = False
        self.logger.info("Live trading stopped")
    
    def _market_data_loop(self):
        """行情数据循环"""
        while self.is_running:
            try:
                # 获取实时行情
                market_data = self._get_market_data()
                
                # 调用策略回调
                signals = self.strategy_callback(market_data)
                
                # 处理交易信号
                self._process_signals(signals)
                
                time.sleep(1)  # 1秒间隔
                
            except Exception as e:
                self.logger.error(f"Market data loop error: {e}")
    
    def _trading_loop(self):
        """交易执行循环"""
        while self.is_running:
            try:
                # 检查订单状态
                self._check_order_status()
                
                # 更新持仓
                self._update_positions()
                
                time.sleep(5)  # 5秒间隔
                
            except Exception as e:
                self.logger.error(f"Trading loop error: {e}")
    
    def place_order(self, symbol: str, quantity: int,
                   action: str = "BUY", order_type: str = "MKT",
                   limit_price: float = None) -> str:
        """下单 - 使用tigeropen标准方式"""
        if not self.trade_client:
            self.logger.error("TradeClient不可用，无法下单")
            return None

        if not self.account:
            self.logger.error("账户信息不可用，无法下单")
            return None

        if not self.risk_manager.check_trade_allowed(
            symbol, abs(quantity), self.positions):
            return None

        try:
            # 创建合约
            contract = stock_contract(symbol, currency="USD")
            
            # 创建订单
            if order_type == "MKT":
                order = market_order(
                    account=self.account,
                    contract=contract,
                    action=action,
                    quantity=abs(quantity)
                )
            else:  # LMT
                if limit_price is None:
                    raise ValueError("Limit price required for limit orders")
                order = limit_order(
                    account=self.account,
                    contract=contract,
                    action=action,
                    quantity=abs(quantity),
                    limit_price=limit_price
                )
            
            # 设置订单有效期
            order.time_in_force = 'DAY'  # 当日有效
            
            # 提交订单
            self.trade_client.place_order(order)

            # 记录订单
            order_data = {
                'symbol': symbol,
                'strategy': getattr(self.strategy_callback, '__name__', 'unknown'),
                'action': action,
                'quantity': abs(quantity),
                'order_type': order_type,
                'price': limit_price or 0,
                'status': 'SUBMITTED',
                'notes': f'Order placed via LiveTrader'
            }

            order_record_id = self.trade_recorder.record_order(order_data)

            # 跟踪活跃订单
            self.active_orders[order.id] = {
                'record_id': order_record_id,
                'symbol': symbol,
                'action': action,
                'quantity': abs(quantity),
                'create_time': datetime.now()
            }

            self.logger.info(f"Order placed: {symbol}, {action}, {quantity}, {order.id}")
            return order.id
            
        except Exception as e:
            self.logger.error(f"Order placement failed: {e}")
            return None
    
    def _get_market_data(self) -> Dict:
        """获取市场数据"""
        try:
            # 这里应该实现具体的市场数据获取逻辑
            # 可以使用quote_client获取实时行情
            return {}
        except Exception as e:
            self.logger.error(f"Failed to get market data: {e}")
            return {}
    
    def _process_signals(self, signals: List[Dict]):
        """处理交易信号"""
        for signal in signals:
            symbol = signal.get('symbol')
            action = signal.get('action')  # BUY/SELL
            quantity = signal.get('quantity')
            
            if symbol and action and quantity:
                self.place_order(symbol, quantity, action)
    
    def _check_order_status(self):
        """检查订单状态"""
        if not self.trade_client:
            return

        try:
            # 检查所有活跃订单的状态
            for order_id, order_info in list(self.active_orders.items()):
                try:
                    # 获取订单状态（这里简化处理，实际应该调用API）
                    # order_status = self.trade_client.get_order(order_id)

                    # 模拟订单完成（实际应该根据API返回判断）
                    # 这里需要根据实际的Tiger API来实现

                    # 更新订单记录
                    update_data = {
                        'status': 'FILLED',  # 实际应该从API获取
                        'filled_quantity': order_info['quantity'],
                        'avg_fill_price': 100.0  # 实际应该从API获取
                    }

                    self.trade_recorder.update_order(order_info['record_id'], update_data)

                    # 记录交易
                    self._record_trade_completion(order_id, order_info, update_data)

                    # 从活跃订单中移除
                    del self.active_orders[order_id]

                except Exception as order_error:
                    self.logger.error(f"检查订单 {order_id} 状态失败: {order_error}")

        except Exception as e:
            self.logger.error(f"Failed to check order status: {e}")

    def _record_trade_completion(self, order_id: str, order_info: Dict, fill_data: Dict):
        """记录交易完成"""
        try:
            # 检查是否有对应的开仓/平仓交易
            symbol = order_info['symbol']
            action = order_info['action']
            quantity = fill_data['filled_quantity']
            fill_price = fill_data['avg_fill_price']

            # 更新持仓记录
            if symbol not in self.open_positions:
                self.open_positions[symbol] = {
                    'quantity': 0,
                    'avg_price': 0,
                    'entry_time': None,
                    'entry_orders': []
                }

            position = self.open_positions[symbol]

            if action == 'BUY':
                # 买入操作
                if position['quantity'] == 0:
                    # 新开仓
                    position['quantity'] = quantity
                    position['avg_price'] = fill_price
                    position['entry_time'] = datetime.now()
                    position['entry_orders'] = [order_id]
                else:
                    # 加仓
                    total_cost = position['quantity'] * position['avg_price'] + quantity * fill_price
                    position['quantity'] += quantity
                    position['avg_price'] = total_cost / position['quantity']
                    position['entry_orders'].append(order_id)

            elif action == 'SELL' and position['quantity'] > 0:
                # 卖出操作 - 记录完整交易
                trade_data = {
                    'symbol': symbol,
                    'strategy': getattr(self.strategy_callback, '__name__', 'unknown'),
                    'action': 'LONG',  # 多头交易
                    'quantity': min(quantity, position['quantity']),
                    'entry_price': position['avg_price'],
                    'exit_price': fill_price,
                    'entry_time': position['entry_time'],
                    'exit_time': datetime.now(),
                    'commission': 2.0,  # 简化处理，实际应该计算
                    'trade_type': 'LONG',
                    'notes': f'Trade completed via LiveTrader'
                }

                self.trade_recorder.record_trade(trade_data)

                # 更新持仓
                position['quantity'] -= min(quantity, position['quantity'])
                if position['quantity'] == 0:
                    # 清空持仓
                    position['avg_price'] = 0
                    position['entry_time'] = None
                    position['entry_orders'] = []

            # 记录持仓快照
            position_data = {
                'symbol': symbol,
                'strategy': getattr(self.strategy_callback, '__name__', 'unknown'),
                'quantity': position['quantity'],
                'avg_price': position['avg_price'],
                'market_value': position['quantity'] * fill_price,
                'unrealized_pnl': (fill_price - position['avg_price']) * position['quantity'] if position['quantity'] > 0 else 0,
                'realized_pnl': 0,  # 简化处理
                'total_pnl': 0  # 简化处理
            }

            self.trade_recorder.record_position(position_data)

        except Exception as e:
            self.logger.error(f"记录交易完成失败: {e}")
    
    def _update_positions(self):
        """更新持仓信息"""
        if not self.trade_client:
            return

        try:
            positions = self.trade_client.get_positions(
                sec_type=SecurityType.STK,
                market=Market.US
            )

            self.positions = {}
            for pos in positions:
                self.positions[pos.contract.symbol] = pos.quantity

        except Exception as e:
            self.logger.error(f"Failed to update positions: {e}")
    
    def get_positions(self) -> Dict[str, float]:
        """获取当前持仓"""
        return self.positions.copy()
    
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        if not self.trade_client:
            self.logger.error("TradeClient不可用，无法撤销订单")
            return False

        try:
            self.trade_client.cancel_order(self.account, id=order_id)
            self.logger.info(f"Order cancelled: {order_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
