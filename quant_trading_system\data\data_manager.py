import yfinance as yf
import pandas as pd
from tigeropen.tiger_open_client import TigerOpenClient
from tigeropen.quote.quote_client import QuoteClient
from tigeropen.common.consts import Market, BarPeriod
from typing import Dict, List, Optional, Union
import sqlite3
from datetime import datetime, timedelta
import logging
import time
import os
import glob
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
import schedule
import threading
from pathlib import Path

class DataManager:
    """
    数据管理器 - 支持多时间周期数据下载、保存和查看

    功能特性:
    1. 支持多时间周期数据下载（1分钟到月线）
    2. 支持所有美股股票数据下载
    3. 数据CSV格式保存和查看
    4. 定时任务自动更新数据
    5. 一键导出、迁移数据
    """

    # 支持的时间周期映射
    TIMEFRAMES = {
        '1m': '1m',      # 1分钟
        '5m': '5m',      # 5分钟
        '15m': '15m',    # 15分钟
        '30m': '30m',    # 30分钟
        '1h': '1h',      # 1小时
        '1d': '1d',      # 日线
        '1wk': '1wk',    # 周线
        '1mo': '1mo'     # 月线
    }

    def __init__(self, tiger_client: TigerOpenClient = None, use_csv: bool = True):
        """
        初始化数据管理器

        Args:
            tiger_client: Tiger API客户端
            use_csv: 是否使用CSV存储（默认True）
        """
        self.tiger_client = tiger_client
        self.quote_client = None
        self.use_csv = use_csv
        self.csv_dir = "data/csv"
        self.db_path = "data/market_data.db"
        self.logger = logging.getLogger(__name__)

        # 创建数据目录结构
        self._init_directories()

        # 安全地初始化QuoteClient
        self._init_quote_client()

        # 初始化数据库（如果需要）
        if not self.use_csv:
            self._init_database()

        # 定时任务调度器
        self.scheduler_running = False
        self.scheduler_thread = None

    def _init_directories(self):
        """初始化目录结构"""
        try:
            os.makedirs("data", exist_ok=True)
            if self.use_csv:
                os.makedirs(self.csv_dir, exist_ok=True)
                # 为每个时间周期创建子目录
                for timeframe in self.TIMEFRAMES.keys():
                    os.makedirs(f"{self.csv_dir}/{timeframe}", exist_ok=True)
            self.logger.info("数据目录初始化完成")
        except Exception as e:
            self.logger.error(f"目录初始化失败: {e}")
            raise

    def _init_quote_client(self):
        """安全地初始化QuoteClient"""
        if self.tiger_client:
            try:
                self.quote_client = QuoteClient(self.tiger_client)
                self.logger.info("QuoteClient初始化成功")
            except Exception as e:
                self.logger.warning(f"QuoteClient初始化失败: {e}")
                self.quote_client = None

    def get_csv_filepath(self, symbol: str, timeframe: str) -> str:
        """
        获取CSV文件路径

        Args:
            symbol: 股票代码
            timeframe: 时间周期

        Returns:
            CSV文件完整路径
        """
        if timeframe not in self.TIMEFRAMES:
            raise ValueError(f"不支持的时间周期: {timeframe}")

        return f"{self.csv_dir}/{timeframe}/{symbol}.csv"

    def download_stock_data(self, symbol: str, timeframe: str = '1d',
                           period: str = '1y', start_date: str = None,
                           end_date: str = None, save_to_csv: bool = True) -> pd.DataFrame:
        """
        下载单个股票的历史数据

        Args:
            symbol: 股票代码
            timeframe: 时间周期 ('1m', '5m', '15m', '30m', '1h', '1d', '1wk', '1mo')
            period: 数据周期 ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            save_to_csv: 是否保存到CSV文件

        Returns:
            包含股票数据的DataFrame
        """
        try:
            self.logger.info(f"开始下载 {symbol} {timeframe} 数据")

            # 验证时间周期
            if timeframe not in self.TIMEFRAMES:
                raise ValueError(f"不支持的时间周期: {timeframe}")

            # 创建yfinance ticker对象
            ticker = yf.Ticker(symbol)

            # 下载数据
            if start_date and end_date:
                data = ticker.history(start=start_date, end=end_date, interval=timeframe)
            else:
                data = ticker.history(period=period, interval=timeframe)

            if data.empty:
                self.logger.warning(f"未获取到 {symbol} 的数据")
                return pd.DataFrame()

            # 数据预处理
            data = self._preprocess_data(data, symbol)

            # 保存到CSV
            if save_to_csv and self.use_csv:
                self._save_to_csv(data, symbol, timeframe)

            # 保存到数据库（如果启用）
            if not self.use_csv:
                self._save_to_db(data, symbol, timeframe)

            self.logger.info(f"成功下载 {symbol} {timeframe} 数据，共 {len(data)} 条记录")
            return data

        except Exception as e:
            self.logger.error(f"下载 {symbol} 数据失败: {e}")
            return pd.DataFrame()

    def _preprocess_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        数据预处理

        Args:
            data: 原始数据
            symbol: 股票代码

        Returns:
            预处理后的数据
        """
        try:
            # 重置索引，将日期作为列
            data = data.reset_index()

            # 添加股票代码列
            data['Symbol'] = symbol

            # 重命名列名为标准格式
            column_mapping = {
                'Date': 'Date',
                'Datetime': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume',
                'Dividends': 'Dividends',
                'Stock Splits': 'Stock_Splits'
            }

            # 只保留存在的列
            existing_columns = {k: v for k, v in column_mapping.items() if k in data.columns}
            data = data.rename(columns=existing_columns)

            # 确保必要的列存在
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Symbol']
            for col in required_columns:
                if col not in data.columns:
                    if col == 'Volume':
                        data[col] = 0
                    elif col in ['Open', 'High', 'Low', 'Close']:
                        data[col] = 0.0

            # 按日期排序
            data = data.sort_values('Date').reset_index(drop=True)

            return data

        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            return data

    def _save_to_csv(self, data: pd.DataFrame, symbol: str, timeframe: str):
        """
        保存数据到CSV文件

        Args:
            data: 数据DataFrame
            symbol: 股票代码
            timeframe: 时间周期
        """
        try:
            filepath = self.get_csv_filepath(symbol, timeframe)

            # 如果文件已存在，合并数据并去重
            if os.path.exists(filepath):
                existing_data = pd.read_csv(filepath)
                # 合并数据
                combined_data = pd.concat([existing_data, data], ignore_index=True)
                # 按日期去重，保留最新的数据
                combined_data = combined_data.drop_duplicates(subset=['Date'], keep='last')
                combined_data = combined_data.sort_values('Date').reset_index(drop=True)
                data = combined_data

            # 保存到CSV
            data.to_csv(filepath, index=False)
            self.logger.debug(f"数据已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")

    def load_stock_data(self, symbol: str, timeframe: str = '1d',
                       start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        从CSV文件加载股票数据

        Args:
            symbol: 股票代码
            timeframe: 时间周期
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            股票数据DataFrame
        """
        try:
            filepath = self.get_csv_filepath(symbol, timeframe)

            if not os.path.exists(filepath):
                self.logger.warning(f"数据文件不存在: {filepath}")
                return pd.DataFrame()

            # 读取CSV文件
            data = pd.read_csv(filepath)

            # 转换日期列
            data['Date'] = pd.to_datetime(data['Date'])

            # 按日期范围筛选
            if start_date:
                start_date = pd.to_datetime(start_date)
                data = data[data['Date'] >= start_date]

            if end_date:
                end_date = pd.to_datetime(end_date)
                data = data[data['Date'] <= end_date]

            self.logger.info(f"成功加载 {symbol} {timeframe} 数据，共 {len(data)} 条记录")
            return data

        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()

    def get_us_stock_list(self, market_cap_min: float = 1e9) -> List[str]:
        """
        获取美股股票列表

        Args:
            market_cap_min: 最小市值（默认10亿美元）

        Returns:
            股票代码列表
        """
        try:
            # 获取S&P 500成分股
            sp500_url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            sp500_table = pd.read_html(sp500_url)[0]
            sp500_symbols = sp500_table['Symbol'].tolist()

            # 获取NASDAQ 100成分股
            nasdaq100_url = "https://en.wikipedia.org/wiki/Nasdaq-100"
            nasdaq100_table = pd.read_html(nasdaq100_url)[4]  # 第5个表格是成分股列表
            nasdaq100_symbols = nasdaq100_table['Ticker'].tolist()

            # 合并并去重
            all_symbols = list(set(sp500_symbols + nasdaq100_symbols))

            # 过滤掉包含特殊字符的股票代码
            filtered_symbols = [s for s in all_symbols if s.isalpha() and len(s) <= 5]

            self.logger.info(f"获取到 {len(filtered_symbols)} 个美股股票代码")
            return filtered_symbols

        except Exception as e:
            self.logger.error(f"获取美股股票列表失败: {e}")
            # 返回一些常见的美股股票作为备选
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'ADBE', 'CRM']

    def batch_download_stocks(self, symbols: List[str], timeframes: List[str] = ['1d'],
                             period: str = '1y', max_workers: int = 5) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        批量下载多个股票的多时间周期数据

        Args:
            symbols: 股票代码列表
            timeframes: 时间周期列表
            period: 数据周期
            max_workers: 最大并发数

        Returns:
            嵌套字典 {symbol: {timeframe: DataFrame}}
        """
        results = {}
        total_tasks = len(symbols) * len(timeframes)
        completed_tasks = 0

        self.logger.info(f"开始批量下载 {len(symbols)} 个股票的 {len(timeframes)} 个时间周期数据")

        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有下载任务
                future_to_task = {}
                for symbol in symbols:
                    results[symbol] = {}
                    for timeframe in timeframes:
                        future = executor.submit(
                            self.download_stock_data,
                            symbol, timeframe, period
                        )
                        future_to_task[future] = (symbol, timeframe)

                # 收集结果
                for future in as_completed(future_to_task):
                    symbol, timeframe = future_to_task[future]
                    try:
                        data = future.result()
                        results[symbol][timeframe] = data
                        completed_tasks += 1

                        # 显示进度
                        progress = (completed_tasks / total_tasks) * 100
                        self.logger.info(f"下载进度: {progress:.1f}% ({completed_tasks}/{total_tasks})")

                    except Exception as e:
                        self.logger.error(f"下载 {symbol} {timeframe} 失败: {e}")
                        results[symbol][timeframe] = pd.DataFrame()

            self.logger.info(f"批量下载完成，成功下载 {completed_tasks}/{total_tasks} 个任务")
            return results

        except Exception as e:
            self.logger.error(f"批量下载失败: {e}")
            return results

    def start_auto_update_scheduler(self, symbols: List[str] = None,
                                   timeframes: List[str] = ['1d'],
                                   update_time: str = "09:30"):
        """
        启动定时任务自动更新数据

        Args:
            symbols: 股票代码列表（None则使用美股列表）
            timeframes: 时间周期列表
            update_time: 更新时间 (HH:MM格式)
        """
        try:
            if self.scheduler_running:
                self.logger.warning("定时任务已在运行")
                return

            if symbols is None:
                symbols = self.get_us_stock_list()

            # 清除之前的任务
            schedule.clear()

            # 设置定时任务
            schedule.every().day.at(update_time).do(
                self._scheduled_update, symbols, timeframes
            )

            # 启动调度器线程
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()

            self.logger.info(f"定时任务已启动，每天 {update_time} 更新 {len(symbols)} 个股票数据")

        except Exception as e:
            self.logger.error(f"启动定时任务失败: {e}")

    def stop_auto_update_scheduler(self):
        """停止定时任务"""
        try:
            self.scheduler_running = False
            schedule.clear()
            if self.scheduler_thread:
                self.scheduler_thread.join(timeout=5)
            self.logger.info("定时任务已停止")
        except Exception as e:
            self.logger.error(f"停止定时任务失败: {e}")

    def _run_scheduler(self):
        """运行调度器"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    def _scheduled_update(self, symbols: List[str], timeframes: List[str]):
        """定时更新任务"""
        try:
            self.logger.info("开始定时数据更新")
            self.batch_download_stocks(symbols, timeframes, period='5d')  # 更新最近5天数据
            self.logger.info("定时数据更新完成")
        except Exception as e:
            self.logger.error(f"定时数据更新失败: {e}")

    def export_all_data(self, export_path: str = "data_export") -> bool:
        """
        一键导出所有数据

        Args:
            export_path: 导出路径

        Returns:
            是否成功
        """
        try:
            # 创建导出目录
            export_dir = Path(export_path)
            export_dir.mkdir(exist_ok=True)

            # 复制CSV数据目录
            if os.path.exists(self.csv_dir):
                shutil.copytree(self.csv_dir, export_dir / "csv", dirs_exist_ok=True)

            # 复制数据库文件
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, export_dir / "market_data.db")

            # 创建导出信息文件
            export_info = {
                'export_time': datetime.now().isoformat(),
                'csv_enabled': self.use_csv,
                'timeframes': list(self.TIMEFRAMES.keys()),
                'total_files': len(glob.glob(f"{self.csv_dir}/**/*.csv", recursive=True))
            }

            with open(export_dir / "export_info.json", 'w') as f:
                import json
                json.dump(export_info, f, indent=2)

            self.logger.info(f"数据导出完成: {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False

    def import_data(self, import_path: str) -> bool:
        """
        导入数据

        Args:
            import_path: 导入路径

        Returns:
            是否成功
        """
        try:
            import_dir = Path(import_path)
            if not import_dir.exists():
                self.logger.error(f"导入路径不存在: {import_path}")
                return False

            # 导入CSV数据
            csv_import_dir = import_dir / "csv"
            if csv_import_dir.exists():
                if os.path.exists(self.csv_dir):
                    shutil.rmtree(self.csv_dir)
                shutil.copytree(csv_import_dir, self.csv_dir)

            # 导入数据库文件
            db_import_file = import_dir / "market_data.db"
            if db_import_file.exists():
                shutil.copy2(db_import_file, self.db_path)

            self.logger.info(f"数据导入完成: {import_path}")
            return True

        except Exception as e:
            self.logger.error(f"数据导入失败: {e}")
            return False

    def get_data_summary(self) -> Dict:
        """
        获取数据概览

        Returns:
            数据概览字典
        """
        try:
            summary = {
                'timeframes': {},
                'total_symbols': 0,
                'total_files': 0,
                'data_size_mb': 0
            }

            if not os.path.exists(self.csv_dir):
                return summary

            # 统计每个时间周期的数据
            for timeframe in self.TIMEFRAMES.keys():
                timeframe_dir = f"{self.csv_dir}/{timeframe}"
                if os.path.exists(timeframe_dir):
                    csv_files = glob.glob(f"{timeframe_dir}/*.csv")
                    symbols = [os.path.basename(f).replace('.csv', '') for f in csv_files]

                    # 计算数据大小
                    total_size = sum(os.path.getsize(f) for f in csv_files)

                    summary['timeframes'][timeframe] = {
                        'symbols': len(symbols),
                        'files': len(csv_files),
                        'size_mb': round(total_size / (1024 * 1024), 2),
                        'sample_symbols': symbols[:10]  # 显示前10个股票代码
                    }

                    summary['total_files'] += len(csv_files)
                    summary['data_size_mb'] += summary['timeframes'][timeframe]['size_mb']

            # 获取所有唯一股票代码
            all_symbols = set()
            for timeframe_info in summary['timeframes'].values():
                all_symbols.update(timeframe_info.get('sample_symbols', []))
            summary['total_symbols'] = len(all_symbols)

            return summary

        except Exception as e:
            self.logger.error(f"获取数据概览失败: {e}")
            return {}

    def list_available_symbols(self, timeframe: str = '1d') -> List[str]:
        """
        列出可用的股票代码

        Args:
            timeframe: 时间周期

        Returns:
            股票代码列表
        """
        try:
            timeframe_dir = f"{self.csv_dir}/{timeframe}"
            if not os.path.exists(timeframe_dir):
                return []

            csv_files = glob.glob(f"{timeframe_dir}/*.csv")
            symbols = [os.path.basename(f).replace('.csv', '') for f in csv_files]
            symbols.sort()

            return symbols

        except Exception as e:
            self.logger.error(f"获取股票代码列表失败: {e}")
            return []
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            CREATE TABLE IF NOT EXISTS stock_data (
                symbol TEXT,
                date TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                PRIMARY KEY (symbol, date)
            )
        ''')
        conn.close()
    
    def fetch_historical_data(self, symbol: str, period: str = "1y", timeframe: str = "1d") -> pd.DataFrame:
        """
        获取历史数据 - 兼容原有接口

        Args:
            symbol: 股票代码
            period: 数据周期
            timeframe: 时间周期

        Returns:
            股票数据DataFrame
        """
        return self.download_stock_data(symbol, timeframe, period)
    
    def get_realtime_quote(self, symbols: List[str]) -> Dict:
        """获取实时行情 - TigerOpen"""
        if not self.quote_client:
            raise ValueError("TigerOpen client not configured")
        
        try:
            # 分批请求，避免单次请求过多symbol
            REQUEST_SIZE = 50
            all_quotes = {}
            
            for i in range(0, len(symbols), REQUEST_SIZE):
                batch_symbols = symbols[i:i + REQUEST_SIZE]
                quotes = self.quote_client.get_stock_briefs(batch_symbols)
                
                if quotes is not None and not quotes.empty:
                    quote_dict = quotes.set_index('symbol').to_dict('index')
                    all_quotes.update(quote_dict)
                
                # 避免频率限制
                if i + REQUEST_SIZE < len(symbols):
                    time.sleep(0.5)
            
            return all_quotes
            
        except Exception as e:
            self.logger.error(f"Failed to get realtime quotes: {e}")
            return {}
    
    def get_bars(self, symbols: List[str], period: BarPeriod = BarPeriod.DAY, 
                 limit: int = 100) -> pd.DataFrame:
        """获取K线数据"""
        if not self.quote_client:
            raise ValueError("TigerOpen client not configured")
        
        try:
            REQUEST_SIZE = 50
            all_bars = pd.DataFrame()
            
            for i in range(0, len(symbols), REQUEST_SIZE):
                batch_symbols = symbols[i:i + REQUEST_SIZE]
                bars = self.quote_client.get_bars(
                    symbols=batch_symbols,
                    period=period,
                    limit=limit
                )
                
                if bars is not None and not bars.empty:
                    all_bars = pd.concat([all_bars, bars], ignore_index=True)
                
                if i + REQUEST_SIZE < len(symbols):
                    time.sleep(0.5)
            
            return all_bars
            
        except Exception as e:
            self.logger.error(f"Failed to get bars: {e}")
            return pd.DataFrame()
    
    def _save_to_db(self, data: pd.DataFrame, symbol: str, timeframe: str = '1d'):
        """
        保存数据到本地数据库

        Args:
            data: 数据DataFrame
            symbol: 股票代码
            timeframe: 时间周期
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 创建表（如果不存在）
            conn.execute(f'''
                CREATE TABLE IF NOT EXISTS stock_data_{timeframe.replace('m', 'min').replace('h', 'hour').replace('d', 'day').replace('wk', 'week').replace('mo', 'month')} (
                    symbol TEXT,
                    date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    PRIMARY KEY (symbol, date)
                )
            ''')

            # 插入数据
            for _, row in data.iterrows():
                date_str = row['Date'].strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(row['Date']) else ''
                conn.execute(f'''
                    INSERT OR REPLACE INTO stock_data_{timeframe.replace('m', 'min').replace('h', 'hour').replace('d', 'day').replace('wk', 'week').replace('mo', 'month')}
                    (symbol, date, open, high, low, close, volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (symbol, date_str,
                     row.get('Open', 0), row.get('High', 0), row.get('Low', 0),
                     row.get('Close', 0), row.get('Volume', 0)))

            conn.commit()
            conn.close()
            self.logger.debug(f"保存 {len(data)} 条记录到数据库: {symbol} {timeframe}")

        except Exception as e:
            self.logger.error(f"保存数据到数据库失败: {e}")
    
    def get_from_db(self, symbol: str, start_date: str = None, 
                    end_date: str = None) -> pd.DataFrame:
        """从数据库获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = "SELECT * FROM stock_data WHERE symbol = ?"
            params = [symbol]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
            
            query += " ORDER BY date"
            
            data = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if not data.empty:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to get data from DB for {symbol}: {e}")
            return pd.DataFrame()
