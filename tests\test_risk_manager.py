"""
风险管理模块测试
测试高级风险管理功能
"""

import unittest
import os
import shutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import tempfile
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant_trading_system.risk.risk_manager import RiskManager, RiskLimits


class TestRiskLimits(unittest.TestCase):
    """风险限制配置测试"""
    
    def test_default_limits(self):
        """测试默认风险限制"""
        limits = RiskLimits()
        
        # 检查默认值
        self.assertEqual(limits.max_drawdown, 0.1)
        self.assertEqual(limits.max_position_size, 0.05)
        self.assertEqual(limits.max_daily_trades, 50)
        self.assertEqual(limits.max_total_exposure, 0.8)
        self.assertEqual(limits.max_correlation, 0.7)
        self.assertEqual(limits.max_sector_exposure, 0.3)
    
    def test_custom_limits(self):
        """测试自定义风险限制"""
        limits = RiskLimits(
            max_drawdown=0.15,
            max_position_size=0.1,
            max_daily_trades=100,
            blacklist=['RISKY1', 'RISKY2']
        )
        
        self.assertEqual(limits.max_drawdown, 0.15)
        self.assertEqual(limits.max_position_size, 0.1)
        self.assertEqual(limits.max_daily_trades, 100)
        self.assertEqual(limits.blacklist, ['RISKY1', 'RISKY2'])


class TestRiskManager(unittest.TestCase):
    """风险管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建测试风险限制
        self.limits = RiskLimits(
            max_drawdown=0.1,
            max_position_size=0.05,
            max_daily_trades=10,
            max_total_exposure=0.8,
            blacklist=['BLACKLISTED']
        )
        
        self.risk_manager = RiskManager(self.limits)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.risk_manager.limits, RiskLimits)
        self.assertEqual(self.risk_manager.daily_trades, 0)
        self.assertEqual(self.risk_manager.current_drawdown, 0.0)
        self.assertIn('BLACKLISTED', self.risk_manager.blacklist)
        self.assertTrue(os.path.exists(self.risk_manager.risk_data_dir))
    
    def test_basic_risk_checks(self):
        """测试基础风险检查"""
        current_portfolio = {'AAPL': 1000, 'MSFT': 2000}

        # 测试正常交易（小额交易，不超过5%限制）
        result = self.risk_manager._basic_risk_checks('GOOGL', 100, current_portfolio)  # 100/(3000+100) = 3.2% < 5%
        self.assertTrue(result['allowed'])
        self.assertEqual(len(result['reasons']), 0)
        
        # 测试黑名单股票
        result = self.risk_manager._basic_risk_checks('BLACKLISTED', 500, current_portfolio)
        self.assertFalse(result['allowed'])
        self.assertIn('黑名单', result['reasons'][0])
        
        # 测试日交易次数超限
        self.risk_manager.daily_trades = 15  # 超过限制的10次
        result = self.risk_manager._basic_risk_checks('GOOGL', 500, current_portfolio)
        self.assertFalse(result['allowed'])
        self.assertIn('日交易次数超限', result['reasons'][0])
        
        # 重置日交易次数
        self.risk_manager.daily_trades = 0
        
        # 测试回撤超限
        self.risk_manager.current_drawdown = 0.15  # 超过限制的0.1
        result = self.risk_manager._basic_risk_checks('GOOGL', 500, current_portfolio)
        self.assertFalse(result['allowed'])
        self.assertIn('回撤超限', result['reasons'][0])
    
    def test_advanced_risk_checks(self):
        """测试高级风险检查"""
        current_portfolio = {'AAPL': 1000, 'MSFT': 2000}
        
        # 测试正常情况
        result = self.risk_manager._advanced_risk_checks('GOOGL', 500, current_portfolio, 150.0)
        self.assertTrue(result['allowed'])
        
        # 测试高波动率警告
        # 添加价格历史数据模拟高波动率
        self.risk_manager.price_history['GOOGL'] = [100 + 10 * np.sin(i/5) + np.random.normal(0, 5) for i in range(25)]
        
        result = self.risk_manager._advanced_risk_checks('GOOGL', 500, current_portfolio, 150.0)
        # 可能会有高波动率警告
        self.assertIsInstance(result['warnings'], list)
    
    def test_comprehensive_risk_check(self):
        """测试综合风险检查"""
        current_portfolio = {'AAPL': 1000, 'MSFT': 2000}
        
        # 测试正常交易
        result = self.risk_manager.check_trade_allowed('GOOGL', 500, current_portfolio, 150.0)
        self.assertIsInstance(result, dict)
        self.assertIn('allowed', result)
        self.assertIn('reasons', result)
        self.assertIn('warnings', result)
        self.assertIn('risk_score', result)
        
        # 测试被拒绝的交易
        result = self.risk_manager.check_trade_allowed('BLACKLISTED', 500, current_portfolio, 150.0)
        self.assertFalse(result['allowed'])
        self.assertGreater(len(result['reasons']), 0)
    
    def test_update_portfolio(self):
        """测试投资组合更新"""
        positions = {'AAPL': 100, 'MSFT': 200}
        prices = {'AAPL': 150.0, 'MSFT': 250.0}
        
        # 更新投资组合
        self.risk_manager.update_portfolio(positions, prices)
        
        # 检查更新结果
        self.assertEqual(self.risk_manager.positions, positions)
        self.assertIn('AAPL', self.risk_manager.price_history)
        self.assertIn('MSFT', self.risk_manager.price_history)
        
        # 检查价格历史
        self.assertEqual(self.risk_manager.price_history['AAPL'][-1], 150.0)
        self.assertEqual(self.risk_manager.price_history['MSFT'][-1], 250.0)
        
        # 检查组合价值计算
        expected_value = 100 * 150.0 + 200 * 250.0  # 65000
        self.assertEqual(self.risk_manager.current_value, expected_value)
    
    def test_calculate_var(self):
        """测试VaR计算"""
        # 设置测试数据
        positions = {'AAPL': 100, 'MSFT': 200}
        self.risk_manager.positions = positions
        
        # 添加价格历史数据
        np.random.seed(42)  # 确保可重复
        for symbol in positions.keys():
            prices = [100 + np.random.normal(0, 2) for _ in range(25)]
            self.risk_manager.price_history[symbol] = prices
        
        # 计算VaR
        var_5pct = self.risk_manager.calculate_var(0.05)
        var_1pct = self.risk_manager.calculate_var(0.01)
        
        # 检查结果
        self.assertIsInstance(var_5pct, float)
        self.assertIsInstance(var_1pct, float)
        self.assertGreaterEqual(var_5pct, 0)
        self.assertGreaterEqual(var_1pct, 0)
        # 1%VaR应该大于5%VaR
        self.assertGreaterEqual(var_1pct, var_5pct)
    
    def test_generate_risk_report(self):
        """测试风险报告生成"""
        # 设置一些测试数据
        self.risk_manager.positions = {'AAPL': 100, 'MSFT': 200}
        self.risk_manager.current_value = 50000
        self.risk_manager.peak_value = 55000
        self.risk_manager.current_drawdown = 0.09
        self.risk_manager.daily_trades = 5
        
        # 生成报告
        report = self.risk_manager.generate_risk_report()
        
        # 检查报告结构
        self.assertIsInstance(report, dict)
        self.assertIn('timestamp', report)
        self.assertIn('portfolio_summary', report)
        self.assertIn('risk_metrics', report)
        
        # 检查投资组合摘要
        portfolio_summary = report['portfolio_summary']
        self.assertEqual(portfolio_summary['total_positions'], 2)
        self.assertEqual(portfolio_summary['current_value'], 50000)
        self.assertEqual(portfolio_summary['daily_trades'], 5)
        
        # 检查风险指标
        risk_metrics = report['risk_metrics']
        self.assertIn('var_5pct', risk_metrics)
        self.assertIn('var_1pct', risk_metrics)
        self.assertIn('max_drawdown_limit', risk_metrics)
    
    def test_stress_test(self):
        """测试压力测试"""
        # 设置测试数据
        positions = {'AAPL': 100, 'MSFT': 200}
        prices = {'AAPL': 150.0, 'MSFT': 250.0}
        
        self.risk_manager.update_portfolio(positions, prices)
        
        # 定义压力测试场景
        scenarios = [
            {'AAPL': -0.2, 'MSFT': -0.15},  # 市场下跌场景
            {'AAPL': -0.3, 'MSFT': -0.25},  # 严重下跌场景
        ]
        
        # 运行压力测试
        results = self.risk_manager.stress_test(scenarios)
        
        # 检查结果
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 2)
        
        # 检查第一个场景
        scenario_1 = results['Scenario_1']
        self.assertIn('scenario', scenario_1)
        self.assertIn('portfolio_loss', scenario_1)
        self.assertIn('loss_percentage', scenario_1)
        self.assertIn('exceeds_limit', scenario_1)
        
        # 验证损失计算
        expected_loss = 100 * 150.0 * (-0.2) + 200 * 250.0 * (-0.15)
        self.assertAlmostEqual(scenario_1['portfolio_loss'], expected_loss, places=2)
    
    def test_blacklist_management(self):
        """测试黑名单管理"""
        # 添加到黑名单
        self.risk_manager.add_to_blacklist('RISKY_STOCK')
        self.assertIn('RISKY_STOCK', self.risk_manager.blacklist)
        
        # 从黑名单移除
        self.risk_manager.remove_from_blacklist('RISKY_STOCK')
        self.assertNotIn('RISKY_STOCK', self.risk_manager.blacklist)
        
        # 测试原有黑名单项目
        self.assertIn('BLACKLISTED', self.risk_manager.blacklist)
    
    def test_daily_trades_management(self):
        """测试日交易次数管理"""
        # 更新日交易次数
        self.risk_manager.update_daily_trades(5)
        self.assertEqual(self.risk_manager.daily_trades, 5)
        
        # 重置日交易次数
        self.risk_manager.reset_daily_trades()
        self.assertEqual(self.risk_manager.daily_trades, 0)
    
    def test_risk_data_persistence(self):
        """测试风险数据持久化"""
        # 设置一些数据
        positions = {'AAPL': 100}
        prices = {'AAPL': 150.0}
        self.risk_manager.update_portfolio(positions, prices)
        
        # 检查数据文件是否创建
        price_file = f"{self.risk_manager.risk_data_dir}/price_history.json"
        self.assertTrue(os.path.exists(price_file))
        
        # 检查文件内容
        with open(price_file, 'r') as f:
            saved_data = json.load(f)
        
        self.assertIn('AAPL', saved_data)
        self.assertIn(150.0, saved_data['AAPL'])


class TestRiskManagerIntegration(unittest.TestCase):
    """风险管理器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.risk_manager = RiskManager()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_full_risk_workflow(self):
        """测试完整风险管理工作流"""
        # 1. 初始化投资组合
        positions = {'AAPL': 100, 'MSFT': 200, 'GOOGL': 50}
        prices = {'AAPL': 150.0, 'MSFT': 250.0, 'GOOGL': 2500.0}
        
        self.risk_manager.update_portfolio(positions, prices)
        
        # 2. 检查交易是否允许
        result = self.risk_manager.check_trade_allowed('TSLA', 10, positions, 800.0)
        self.assertIsInstance(result, dict)
        
        # 3. 生成风险报告
        report = self.risk_manager.generate_risk_report()
        self.assertIsInstance(report, dict)
        self.assertIn('portfolio_summary', report)
        
        # 4. 运行压力测试
        scenarios = [{'AAPL': -0.1, 'MSFT': -0.1, 'GOOGL': -0.1}]
        stress_results = self.risk_manager.stress_test(scenarios)
        self.assertIsInstance(stress_results, dict)
        
        # 5. 计算VaR
        var = self.risk_manager.calculate_var()
        self.assertIsInstance(var, float)
        self.assertGreaterEqual(var, 0)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
